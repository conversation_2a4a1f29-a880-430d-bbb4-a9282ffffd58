#!/usr/bin/env python3
"""
Test script to verify the changes to the /ask endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_ask_with_claim_no():
    """Test /ask endpoint with claim_no (should use full context)"""
    print("Testing /ask with claim_no (full context mode)...")
    
    response = requests.post(f"{BASE_URL}/ask", json={
        "question": "What was the total approved amount for this claim?",
        "claim_no": 131420765,
        "max_results": 5
    })
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Question: {result['question']}")
        print(f"Answer: {result['answer'][:200]}...")
        print(f"Confidence: {result['confidence_score']}")
        print(f"Chunks: {len(result['relevant_chunks'])}")
        print("✓ Full context mode working")
    else:
        print(f"❌ Error: {response.text}")
    print("-" * 50)

def test_ask_without_claim_no():
    """Test /ask endpoint without claim_no (should use vector search)"""
    print("Testing /ask without claim_no (vector search mode)...")
    
    response = requests.post(f"{BASE_URL}/ask", json={
        "question": "What deductions were applied?",
        "max_results": 3
    })
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Question: {result['question']}")
        print(f"Answer: {result['answer'][:200]}...")
        print(f"Confidence: {result['confidence_score']}")
        print(f"Chunks: {len(result['relevant_chunks'])}")
        print("✓ Vector search mode working")
    else:
        print(f"❌ Error: {response.text}")
    print("-" * 50)

def test_ask_empty_question():
    """Test /ask endpoint with empty question (should fail)"""
    print("Testing /ask with empty question (should fail)...")
    
    response = requests.post(f"{BASE_URL}/ask", json={
        "question": "",
        "claim_no": 131420765
    })
    
    print(f"Status: {response.status_code}")
    if response.status_code == 400:
        print("✓ Empty question properly rejected")
    else:
        print(f"❌ Expected 400, got {response.status_code}: {response.text}")
    print("-" * 50)

def test_ask_missing_question():
    """Test /ask endpoint without question field (should fail)"""
    print("Testing /ask without question field (should fail)...")
    
    response = requests.post(f"{BASE_URL}/ask", json={
        "claim_no": 131420765,
        "max_results": 5
    })
    
    print(f"Status: {response.status_code}")
    if response.status_code == 422:  # Pydantic validation error
        print("✓ Missing question properly rejected")
    else:
        print(f"❌ Expected 422, got {response.status_code}: {response.text}")
    print("-" * 50)

def main():
    """Run all tests"""
    print("Testing /ask endpoint changes...")
    print("=" * 50)
    
    # Test health first
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ API not running or not healthy")
            return
        print("✓ API is healthy")
    except:
        print("❌ Cannot connect to API")
        return
    
    # Run tests
    test_ask_with_claim_no()
    test_ask_without_claim_no()
    test_ask_empty_question()
    test_ask_missing_question()
    
    print("Tests completed!")

if __name__ == "__main__":
    main()
