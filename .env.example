# Google Gemini Configuration (Cheaper than OpenAI)
GEMINI_API_KEY=AIzaSyAjWhIQrslTqw2KoOdB-iQrjY9wiF_kG9Q
GEMINI_MODEL=gemini-1.5-flash

# Vector Database Configuration
CHROMA_PERSIST_DIRECTORY=./chroma_db
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Application Configuration
MAX_TOKENS=4000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# API Configuration
API_HOST=0.0.0.0
API_PORT=8001

# Proclaim API Configuration
PROCLAIM_API_URL=https://proclaim-api-prod.mediassist.in/FetchVikingsRuleEngineLogs/
PROCLAIM_ACCESS_TOKEN=your_proclaim_access_token_here
PROCLAIM_PAYER=MTAwNHxNQTEyMzQ1Ng==
