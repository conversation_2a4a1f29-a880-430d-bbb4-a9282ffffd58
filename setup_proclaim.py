#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to help set up Proclaim API configuration from the curl command
"""

import os
from pathlib import Path

def extract_token_from_curl():
    """Extract the access token from the provided curl command"""
    
    # The access token from your curl command
    access_token = "70b47c92ce225c1c2fd54833ba646c218e0d3975b66ce88842dd11498c1cb415edf635144cea8e4a7d57edc5d8fe0a3f175cdfff8f79a0611ef5552246b5fff867e95a6a46178ae945575e9ed180cee74d83256c6f1daec623abd1dff7c9e7ba5f37f350269c13051f7b04e29601d812445cf5597c6de83f1954f65993404e3d1590414fdb71fa2f7629b714ec2587856d02ad4be14ed1d7c03249b1e86d3b62d7b80b35734d6acca4bd6373d1ee50621c1a695faa72b5b070dd83e3b570a3154f11866c5f81b2de6e2b4876c4297cfbe28a9f8e859617183777a03989bcafdf6284abc63f3de383cfb8e5d06bb8053686cdab7454552a4747f8acb0ab6f6f2fd4b9cd52c3dbf79dd43a1a0a2335c126293f942030adc1c6172551b19e3acea378356daf7023deadbca55802593a21b4290e78c417a957fc31bb78ddcc44dfef3685f600b74f71baf60c0e95f39192f1c451b89b30b8304f5352680c202af81169a97030d0cbf949f0cf7b980137d5982a8d3b7ae6b2f297a8297a6a02730a231dc45c886cf7c646032cb79fe3757de2b134c8633500fe7c7bdbc2627742203afa83c9afe187cda839a200591667d9d6973fed45f4a0a037d52fbbf917476e26e19970ef4943986f4574f20c38205e59c18ae436bb595f7af90ff13141a515c7"
    
    return access_token

def update_env_file():
    """Update the .env file with Proclaim API configuration"""
    
    env_file = Path(".env")
    
    # Read existing .env file or create from example
    if env_file.exists():
        with open(env_file, 'r') as f:
            lines = f.readlines()
    else:
        env_example = Path(".env.example")
        if env_example.exists():
            with open(env_example, 'r') as f:
                lines = f.readlines()
        else:
            print("❌ No .env or .env.example file found")
            return False
    
    # Extract token
    access_token = extract_token_from_curl()
    
    # Update lines with Proclaim configuration
    updated_lines = []
    token_updated = False
    
    for line in lines:
        if line.startswith("PROCLAIM_ACCESS_TOKEN="):
            updated_lines.append(f"PROCLAIM_ACCESS_TOKEN={access_token}\n")
            token_updated = True
        else:
            updated_lines.append(line)
    
    # If token line wasn't found, add it
    if not token_updated:
        updated_lines.append(f"\n# Proclaim API Configuration\n")
        updated_lines.append(f"PROCLAIM_API_URL=https://proclaim-api-prod.mediassist.in/FetchVikingsRuleEngineLogs/\n")
        updated_lines.append(f"PROCLAIM_ACCESS_TOKEN={access_token}\n")
        updated_lines.append(f"PROCLAIM_PAYER=MTAwNHxNQTEyMzQ1Ng==\n")
    
    # Write updated .env file
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)
    
    print("✅ Updated .env file with Proclaim API configuration")
    return True

def test_configuration():
    """Test the Proclaim API configuration"""
    try:
        from proclaim_client import ProclaimAPIClient
        
        client = ProclaimAPIClient()
        
        print("\n🔍 Testing Proclaim API configuration...")
        
        if client.validate_configuration():
            print("✅ Configuration is valid")
            
            print("🔗 Testing connection...")
            if client.test_connection():
                print("✅ Connection test successful!")
                print("\n🎉 Proclaim API is ready to use!")
                return True
            else:
                print("❌ Connection test failed")
                print("   Please check your access token and network connection")
                return False
        else:
            print("❌ Configuration is invalid")
            return False
            
    except ImportError:
        print("⚠️  Cannot test configuration - dependencies not installed")
        print("   Run: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def main():
    """Main setup function"""
    print("🔧 Proclaim API Setup")
    print("=" * 40)
    
    print("This script will configure your .env file with the Proclaim API settings")
    print("from the curl command you provided.\n")
    
    # Update .env file
    if update_env_file():
        print("\n📋 Configuration Summary:")
        print("- API URL: https://proclaim-api-prod.mediassist.in/FetchVikingsRuleEngineLogs/")
        print("- Access Token: ✅ Set (long token)")
        print("- Payer: MTAwNHxNQTEyMzQ1Ng==")
        
        # Test configuration
        test_configuration()
        
        print("\n📝 Next Steps:")
        print("1. Make sure you have a Gemini API key in your .env file:")
        print("   GEMINI_API_KEY=your_gemini_api_key_here")
        print("\n2. Start the server:")
        print("   python main.py")
        print("\n3. Test fetching claims:")
        print("   python test_api.py")
        print("\n4. Or use the web interface:")
        print("   http://localhost:8001/static/index.html")
        
    else:
        print("❌ Failed to update .env file")

if __name__ == "__main__":
    main()
