{"DeductionsInClaim": [{"slNo": 67166164, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 9402.0, "remarks": "Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57 on Rs. 103440"}, {"slNo": 67166165, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 10000.0, "remarks": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay) on Rs. 103440"}, {"deductionDesc": "nonMedicalDeductions", "amount": 32466, "reasons": [{"id": 312819731, "typeDesc": "Pharmacy & Medicine Charges", "for": "iv fluids / disposables", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 11438, "payableAmt": 3407, "dedReasonDesc": "tegaderm 1633 (7x8.5cm) for cannula hsn:30051020 ):-214.00,venflon i i.v canulla-20g(bd) ( hsn:90183930 ):-342.50,accu-chek safe-t-pr0 uno ( hsn:90183990 ):-345.00,dc face mask 3 layer loop bulk 50s (doctor choice) hsn:62103090 ):-120.00,sensi care nitrile p/f exam gloves-m 100s (mediline) (:-1800.00,syringes 10ml bd (discardit) ( hsn:90183100 ):-185.00,n95 venus v-4400 niosh flat ffr mask (venus) hsn:63079090 ):-285.00,syringe 2 ml w/n. (nipro) ( hsn:90183990 ):-22.00,(b) accucheck performa (12062967 ):-180.00,gastroenterology disposable mouth piece (15411814 ):-150.00,(b) accucheck performa (12067846 ):-180.00,(b) accucheck performa (12073003 ):-180.00,b 0 syringes 20ml (discard) ( hsn:90183990 ) (pmis2243673227:-162.00,syringe 3ml nipro ( hsn:90183990 ) (pmis2243620620 ):-15.00,tegaderm 1633 (7x8.5cm) for cannula (hsn:30051020 ):-214.00,ultra fine minipen needles (b.d)# ( hsn:90183220 ):-41.20,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090 ):-30.00,dc under pad 60x90cm hsn:48182000 ):-99.00,gauze swabs 7.5 7.5cm 12ply(sterile): # (:-132.00,gauze swabs 7.5 7.5cm 12ply(sterile) # (:-132.00,tegaderm 1657 (8.5x11.5cm) for central line hsn:30051020:-1604.00,dc face mask 3 layer loop bulk 50s (doctor choice) (hsn:62103090:-50.00,dc under pad 60x90cm ( hsn:48182000 ):-99.00,ecg electrodes (3m health care) ( hsn:90189099 ):-94.50,flexi mask (adult) ## hsn:90189099 ):-331.00,oxy set adult nasal oxygen catheter sh-2016s (romsons):-275.00,sensi care nitrile p/f exam gloves-m 100s (mediline):-400.00,surgeons cap (dchoice) ( hsn:62103090 ):-37.50,syringe 3ml nipro hsn:90183990 (pmis2243644051 ):-15.00,syringes 10ml bd (discardit) ( hsn:90183100:-74.00,syringes 5ml b.d. hsn:90183100 (pmis2243644051:-42.00,accucheck performa :-180.00", "nonPayableAmt": 8031, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 213}, {"id": 312819740, "typeDesc": "Miscellaneous Charges", "for": "other miscellaneous charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 13380, "payableAmt": 0, "dedReasonDesc": "hospital services and treatment monitoring and management charges(private):-13200.00,ENDOSCOPY PRINTOUT CHARGES:-180.00", "nonPayableAmt": 13380, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 5}, {"id": 312819744, "typeDesc": "Miscellaneous Charges", "for": "documentation charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 800, "payableAmt": 0, "dedReasonDesc": "medical records:-800.00", "nonPayableAmt": 800, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 312819732, "typeDesc": "Pharmacy & Medicine Charges", "for": "medicines/drugs", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 2952, "payableAmt": 2502, "dedReasonDesc": "dc cutarub-chg 500ml with pump ( hsn:30041030:-450.00", "nonPayableAmt": 450, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 58}, {"id": 312819733, "typeDesc": "Hospital Charges", "for": "gst on room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1209, "payableAmt": 1031, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-177.72", "nonPayableAmt": 178, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 2}, {"id": 312819734, "typeDesc": "Hospital Charges", "for": "room rent", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 26000, "payableAmt": 22178, "dedReasonDesc": "Excess of Room rent charges for higher room opted:-3822.00", "nonPayableAmt": 3822, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 4}, {"id": 312819735, "typeDesc": "Consultant Charges", "for": "consultation / visit", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 18134, "payableAmt": 12840, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-2212.78,dr. nithya m (psychiatry ):-2151.00,nutritional and functional assessment:-930.00", "nonPayableAmt": 5294, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 8}, {"id": 312819736, "typeDesc": "Hospital Charges", "for": "ventilator charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1000, "payableAmt": 853, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-147.00", "nonPayableAmt": 147, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 1}, {"id": 312819737, "typeDesc": "Surgery Charges", "for": "other surgical item charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1400, "payableAmt": 1194, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-205.80", "nonPayableAmt": 206, "packageID": 0, "isDeleted": false, "ctype": 9, "clmBillLOS": 1}, {"id": 312819741, "typeDesc": "Consultant Charges", "for": "anaesthaesthist charges", "date": "/Date(1749148200000+0530)/", "number": "Not Available", "amt": 1075, "payableAmt": 917, "dedReasonDesc": "Proportionate Deduction due to Higher Room Rent Occupied:-158.02", "nonPayableAmt": 158, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 1}]}], "Deduction reasons": [{"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 103439.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "NonMedicalExpense": {"rules": [{"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}]}], "count": 1, "category_name": "NonMedicalExpense"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 85.30%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}]}], "count": 2, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 10000"}, {"message": "Copay deduction amount is 10000"}, {"message": "Copay deduction amount is: 10000"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 10%  of admissible amount 106163.96 and flat amount 10000 is 10000 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 8905.18"}, {"message": "HMOU Discount Calculated: 9402.08"}, {"message": "MAX Discount Calculated: 9402.08"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Pharmacy & Medicine Charges: 2760.22,Hospital Charges: 1827.11,Consultant Charges: 1299.90,Surgery Charges: 94.74,Investigation & Lab Charges: 2460.50,Miscellaneous Charges: 959.57"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "NonMedicalExpense": {"rules": [{"category": "NonMedicalExpense", "subCategory": "OverrideNMEParticularsAmount", "subCategoryDisplayName": "NME limit by Subcharge", "reasons": [{"message": "Limit applicable subcharge head: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "NME limit by Subcharge - Execution starts"}, {"message": "NME limit by Subcharge - Execution ends"}]}], "count": 1, "category_name": "NonMedicalExpense"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 2"}, {"message": "Matching Config Count: 2"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:5500.0"}, {"message": "Tariff ICU Room Rent From Master:5500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 4000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category SemiPvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:3400.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:11100.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category Found Under Sharing Category"}, {"message": "Found room rent details under SHARING Category"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category not found Under PRIVATE Category"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room,room rent,room rent (per day),associated room charges,duty doctor,injection / iv administration charge,gst on room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges,icu room rent,icu injection / iv administration charge,intencivist charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 4.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Non-Tariff bill as insurer Perday ChargeType :gst on room rent , Units : 4.00, PerDay Price : 604"}, {"message": "Tariff Bill Price as per insurer :5500.0"}, {"message": "Non-Tariff bill Price as per insurer :302"}, {"message": "Total Room Price as per insurer :5802.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 5802.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 5802.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 5802.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 4.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 5802.26"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 23208.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}, {"category": "Repudiation", "subCategory": "RepudiationByHospital", "subCategoryDisplayName": "Repudiation By Hospital And Claim Type", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Matching Config Count: 1"}, {"message": "Repudiation By Hospital And Claim Type - Execution starts"}, {"message": "Repudiation By Hospital And Claim Type - Execution ends"}]}], "count": 2, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:48:16.594364", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:48:16.594695", "total_logs": 2, "claim_logs_count": 2}}], "BasicClaimInformation": {"isClaimLocked": false, "isTechError": false, "isSuspected": false, "isInwarded": false, "eventId": *********, "insurerClmRefNo": "TP00392000025900728404", "infoReqSt1Date": "/Date(-621***********)/", "infoReqSt2Date": "/Date(-621***********)/", "docRecDate": "/Date(-621***********+0553)/", "appDate": "/Date(-621***********)/", "settlementDate": "/Date(-621***********)/", "actualReceivedDate": "/Date(1748951632000+0530)/", "denialDate": "/Date(-621***********)/", "closedDate": "/Date(-621***********)/", "clsDate": "/Date(-621***********)/", "netPayAmount": "0", "taxAmount": "0", "chequeAmount": "0", "serviceTaxAmt": "0", "admissibleAmt": "0", "approverRemarks": "-", "id": *********, "typeDesc": "Cashless", "type": 3, "statusName": "Received", "statusId": 2, "receivedDate": "/Date(1748951632000+0530)/", "preAuths": "*********", "compRefNo": "113382_38538", "irDate": "/Date(-621***********)/", "settledDate": "/Date(-621***********)/", "latestAuditDate": "/Date(-621***********)/", "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "doctorName": "Dr. RAVIKUMAR N R", "lengthofStay": 5, "amount": 145308, "approvedAmt": 93440, "paApprovedAmt": "93,440", "varianceAmt": "0", "contactNumber": "99******88", "unMaskContactNumber": "**********", "mainNumber": *********, "claimRegionId": 14, "reciptdate": "01-Jan-0001", "epd": 0, "roomCategory": "Single private room", "accountVerified": "Account verified", "lastScanDocRecdate": "/Date(*************+0530)/", "latestPreAuthStatusId": 0, "roomCategoryId": 26, "isClaimLCM": 0, "clmSourceName": "ClaimBook", "claimMainClaim": 0, "claimExternalStatus": "<PERSON><PERSON><PERSON>", "claimInsurerStatus": "Pending for data entry", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"1\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":\"1\",\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":5802,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true}", "clmEligibleRoomCategory": 26, "eligibleRoomType": "Single private room", "clmTotalCostofCare": 145308, "clmHospDiscountOnBill": 0, "domiIrSplit": false}, "AilmentSummary": {"code": "K29.60", "group": "", "treatmentName": "Conservative Management", "descs": "Other gastritis without bleeding", "summary": ".Other gastritis without bleeding", "ids": "12864", "ailmentBodySystem": "Digestive System"}, "HospitalDetails": {"id": 113382, "name": "Apollo Speciality Hospital", "cityId": 0, "city": "Chennai", "stateId": 0, "state": "Tamil Nadu", "pincode": "600095", "isPPN": "GIPSA", "typeOfCare": "Tertiary", "hospTariffURL": "http://mawire.mediassistindia.com/HN/PACKAGE%20TARIFF/GIPSA%20PPN/CHENNAI/Apollospeciality%20113382%2022jan16upayanambakkamchennaiTNppnpack.zip", "hospGeneralTariffURL": "http://*************/HN/PACKAGE%20TARIFF/TARIFF%20PRICES%20OF%20HOSPITALS/Apollosplty%20113382%204apr19upVanagaramTNtariff.zip", "hospisVerified": true}, "PatientAndPolicyDetails": {"policyHeader": {"polId": 63241220, "polCorpId": 1006639, "groupCorpId": 1049400, "polNo": "92000063250400000025", "polDevelopmentOfficer": "", "polDevelopmentAgent": "", "polInsCompanyID": 1, "polTypeID": 1, "polSubTypeID": 100, "polInsCompanyName": "The New India Assurance Co. Ltd", "polStartDate": "/Date(1743445800000+0530)/", "polEndDate": "/Date(1774895400000+0530)/", "polHldrName": "Tata Consultancy Services Ltd", "isFloater": false, "isRetail": false, "isMixed": false, "isActive": true, "polFloaterType": "Non Floater", "polTPAId": 93, "polSumInsured": "0", "polPaymentDirect": false, "polBlackListed": false, "maServicingBranchId": "2", "polTotalPremium": 0, "isPartialPending": false, "polRenewalRating": 0, "polCategoryId": 0, "polDeleteStatus": false, "tpaName": "Medi Assist Insurance TPA Pvt. Ltd.", "policySequence": 0, "isSuccess": false}, "mediassistId": **********, "active": true, "name": "Dhakshinamoorthy K", "contactNo": "", "sex": "M", "dob": "/Date(-186384600000+0530)/", "age": 61, "relId": 11, "relName": "Father-in-law", "occId": 0, "cumBonusPer": 0, "domiLimit": 6000, "premium": 0, "balanceSI": 0, "sumInsured": 200000, "bsi": 0, "totalSumInsured": 0, "amtSpent": 145184, "amtAuthorised": 54816, "wef": "01-Apr-2025", "areaCode": "Chennai", "email": "sk**@tcs.com", "addedOn": "/Date(-621***********)/", "addedBy": 0, "modifiedUser": 0, "modifiedOn": "/Date(-621***********)/", "benefUserId": 151213808, "unmaskedEmail": "<EMAIL>"}, "MissingDocuments": ["Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory ", "Please provide the detailed case summary with current status of the patient & further plan of management for further approval IV line mandatory "], "_final_structure_metadata": {"structure_applied": ["DeductionsInClaim moved to top level (first priority)", "logs mapped to \"Deduction reasons\"", "claimInfo mapped to \"BasicClaimInformation\"", "ailmentSmry mapped to \"AilmentSummary\"", "hospHeader mapped to \"HospitalDetails\"", "benefDtls mapped to \"PatientAndPolicyDetails\"", "missingDocuments mapped to \"MissingDocuments\""], "final_structure_keys": ["DeductionsInClaim", "Deduction reasons", "BasicClaimInformation", "<PERSON><PERSON>ent<PERSON>ummar<PERSON>", "HospitalDetails", "PatientAndPolicyDetails"], "restructuring_timestamp": "2025-06-14T00:48:16.595143", "purpose": "Create top-level dictionary structure with business-friendly key names"}}