import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Google Gemini Configuration
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")  # Cheapest model

    # Vector Database Configuration
    CHROMA_PERSIST_DIRECTORY = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")

    # Application Configuration
    MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4000"))
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "200"))

    # API Configuration
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", "8001"))

    # External API Configuration
    PROCLAIM_API_URL = os.getenv("PROCLAIM_API_URL", "https://proclaim-api-prod.mediassist.in/FetchVikingsRuleEngineLogs/")
    PROCLAIM_ACCESS_TOKEN = os.getenv("PROCLAIM_ACCESS_TOKEN", "")
    PROCLAIM_PAYER = os.getenv("PROCLAIM_PAYER", "MTAwNHxNQTEyMzQ1Ng==")

settings = Settings()
