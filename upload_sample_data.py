#!/usr/bin/env python3
"""
Script to upload sample claims data to the API
"""

import requests
import json
import time
import sys
from pathlib import Path

API_BASE = "http://localhost:8001"

def wait_for_server(max_attempts=30):
    """Wait for the server to be ready"""
    print("Waiting for server to be ready...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{API_BASE}/health", timeout=5)
            if response.status_code == 200:
                print("✓ Server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"Attempt {attempt + 1}/{max_attempts} - Server not ready yet...")
        time.sleep(2)
    
    print("✗ Server did not become ready in time")
    return False

def upload_sample_data():
    """Upload the sample claims data"""
    sample_file = Path("response.json")
    
    if not sample_file.exists():
        print("✗ Sample data file 'response.json' not found")
        print("Please ensure the file exists in the current directory")
        return False
    
    print("Loading sample data...")
    try:
        with open(sample_file, 'r') as f:
            claims_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"✗ Error reading JSON file: {e}")
        return False
    
    print("Uploading claims data to API...")
    try:
        response = requests.post(
            f"{API_BASE}/upload-claims-json",
            json=claims_data,
            timeout=60  # Give it time for large files
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Upload successful!")
            print(f"  - Processed chunks: {result.get('processed_chunks', 0)}")
            print(f"  - Claims processed: {result.get('claims_processed', [])}")
            print(f"  - Chunk types: {result.get('chunk_types', {})}")
            return True
        else:
            print(f"✗ Upload failed: {response.status_code}")
            print(f"  Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Error uploading data: {e}")
        return False

def test_basic_functionality():
    """Test basic API functionality"""
    print("\nTesting basic functionality...")
    
    # Test stats
    try:
        response = requests.get(f"{API_BASE}/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✓ Database stats: {stats.get('database_stats', {}).get('total_chunks', 0)} chunks")
        else:
            print("⚠ Could not get stats")
    except:
        print("⚠ Error getting stats")
    
    # Test a simple question
    try:
        response = requests.post(f"{API_BASE}/ask", json={
            "question": "What is the claim number?",
            "max_results": 3
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Q&A test successful - Answer: {result['answer'][:100]}...")
        else:
            print("⚠ Q&A test failed")
    except:
        print("⚠ Error testing Q&A")

def main():
    """Main function"""
    print("Claims Data Upload Script")
    print("=" * 30)
    
    # Wait for server
    if not wait_for_server():
        print("\nPlease start the server first:")
        print("  python main.py")
        print("  or")
        print("  python start.py")
        sys.exit(1)
    
    # Upload data
    if upload_sample_data():
        test_basic_functionality()
        
        print("\n" + "=" * 50)
        print("✓ Setup complete! You can now:")
        print("  1. Visit http://localhost:8001/docs for API documentation")
        print("  2. Visit http://localhost:8001/static/index.html for web interface")
        print("  3. Use the API endpoints to ask questions about the claims data")
        print("\nExample questions to try:")
        print("  - What was the total approved amount for this claim?")
        print("  - What deductions were applied and why?")
        print("  - Which rules were executed during claim processing?")
        print("=" * 50)
    else:
        print("\n✗ Upload failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
