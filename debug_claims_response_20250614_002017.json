{"DeductionsInClaim": [{"slNo": 63706822, "claimId": *********, "deductionId": 5, "deductionDesc": "Hospital Discount", "amount": 25558.0, "remarks": "Hospital discount\t"}, {"slNo": 63706824, "claimId": *********, "deductionId": 1, "deductionDesc": "Copay", "amount": 12500.0, "remarks": "Co-pay\t"}, {"slNo": 63706885, "claimId": *********, "deductionId": 3, "deductionDesc": "Policy Excess / Deductible", "amount": 3372.0, "remarks": "Policy Excess \t"}, {"deductionDesc": "nonMedicalDeductions", "amount": 16300, "reasons": [{"id": 279586060, "typeDesc": "Miscellaneous Charges", "for": "documentation charges", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 600, "payableAmt": 0, "dedReasonDesc": "documentation charges:-600.00", "nonPayableAmt": 600, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279586061, "typeDesc": "Miscellaneous Charges", "for": "other miscellaneous charges", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 6100, "payableAmt": 0, "dedReasonDesc": "cardio 3 panel,triage,,97400eu,tes tkit,alere medical:-3600.00,patient linen & laundry charges:-1400.00,tpa evaluation charges:-800.00,common item charges icu:-300.00", "nonPayableAmt": 6100, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 11}, {"id": 279586063, "typeDesc": "Consultant Charges", "for": "consultation / visit", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 48800, "payableAmt": 48100, "dedReasonDesc": "dietician visit:-700.00", "nonPayableAmt": 700, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 31}, {"id": 279586066, "typeDesc": "Consultant Charges", "for": "procedures charges", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 15808, "payableAmt": 14660, "dedReasonDesc": "intravenous cannulation:-1148.00", "nonPayableAmt": 1148, "packageID": 0, "isDeleted": false, "ctype": 10, "clmBillLOS": 20}, {"id": 279586067, "typeDesc": "Pharmacy & Medicine Charges", "for": "external durable appliances", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 626, "payableAmt": 0, "dedReasonDesc": "nebulizer kits,polymed,nebuliser mask, adult,pvc,20111,polymed:-626.00", "nonPayableAmt": 626, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 1}, {"id": 279586070, "typeDesc": "Pharmacy & Medicine Charges", "for": "ot consumables", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 1941, "payableAmt": 1191, "dedReasonDesc": "room-common item charges ward/double/single:-750.00", "nonPayableAmt": 750, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 6}, {"id": 279586051, "typeDesc": "Hospital Charges", "for": "nursing", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 3000, "payableAmt": 0, "dedReasonDesc": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000:-600.000,Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00:-2400.00", "nonPayableAmt": 3000, "packageID": 0, "isDeleted": false, "ctype": 1, "clmBillLOS": 5}, {"id": 279586059, "typeDesc": "Miscellaneous Charges", "for": "admission charges", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 1500, "payableAmt": 0, "dedReasonDesc": "admission charges:-1500.00", "nonPayableAmt": 1500, "packageID": 0, "isDeleted": false, "ctype": 4, "clmBillLOS": 1}, {"id": 279586068, "typeDesc": "Pharmacy & Medicine Charges", "for": "iv fluids / disposables", "date": "/Date(1738175400000+0530)/", "number": "Not Available", "amt": 11706, "payableAmt": 9830, "dedReasonDesc": "electrocardiography ecg) ) 12 lead:-600.00,ecg electrode, adult solid gel,msglt- 11g, medico electrodes international:-216.00,electrode, adult solid gel,msglt- 11g,medic0 electrodes internationa:-180.00,body wipes (pack of 10),glider aqua bath,240mm x300mm,,,,aicare industries pvt ltd:-530.00,bed pan with cover, (disposable) naulakha:-190.00,urine pot naulakha:-160.00", "nonPayableAmt": 1876, "packageID": 0, "isDeleted": false, "ctype": 14, "clmBillLOS": 194}]}], "Deduction reasons": [{"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 214559.73 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 24002.29"}, {"message": "HMOU Discount Calculated: 22369.28"}, {"message": "MAX Discount Calculated: 24002.29"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5958.03,Investigation & Lab Charges: 6095.29,Miscellaneous Charges: 2779.84,Pharmacy & Medicine Charges: 2356.71,Hospital Charges: 5179.40"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 3000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.070990", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100469", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 249428.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 229628.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Miscellaneous Charges: 1380.00,Investigation & Lab Charges: 7684.00,Consultant Charges: 7896.80,Pharmacy & Medicine Charges: 1386.04,Hospital Charges: 7885.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 228028.16 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.84"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 26231.84"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 7885.00,Pharmacy & Medicine Charges: 1386.04,Investigation & Lab Charges: 7684.00,Miscellaneous Charges: 1380.00,Consultant Charges: 7896.80"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.000"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 800.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent,nursing"}, {"message": "ICU : Room Rent based on insurer icu charges,icu nursing charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 22000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.080965", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100480", "total_logs": 5, "claim_logs_count": 5}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 209439.95 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27177.62"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27177.62"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Investigation & Lab Charges: 12690.185955487609769971913110,Consultant Charges: 15132.125244782593237814199880,Hospital Charges: 13263.598678138443488463042630,Pharmacy & Medicine Charges: 3400.6437938635474810680129419,Miscellaneous Charges: 5874.4963277278060226828314438"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "ICU Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 11000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer icu charges"}, {"message": "Normal Room Rent Lenght of Stay : 6.00"}, {"message": "ICU Room Rent Lenght of Stay : 1.00"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11000.0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 6.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 8708.33"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 57000.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 11000.000"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 181449.63 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 27391.37"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 27391.37"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 8471.00,Investigation & Lab Charges: 7104.00,Miscellaneous Charges: 3288.56,Pharmacy & Medicine Charges: 889.06,Hospital Charges: 7638.75"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 66887.500"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.086696", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100482", "total_logs": 3, "claim_logs_count": 3}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 144204.34 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 19468.66"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 19468.66"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5349.00,Investigation & Lab Charges: 6449.00,Miscellaneous Charges: 2855.91,Pharmacy & Medicine Charges: 714.75,Hospital Charges: 4100.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 31500.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.088518", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100484", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 111214.85 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 14611.15"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 14611.15"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 3799.00,Investigation & Lab Charges: 5616.00,Miscellaneous Charges: 1393.10,Pharmacy & Medicine Charges: 653.05,Hospital Charges: 3150.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SemiPrivateRoom"}, {"message": "Tariff Room Rent From Master:6500.0"}, {"message": "Tariff ICU Room Rent From Master:6500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 25000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SemiPrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 6500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.090283", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100485", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 151429.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 21295.81"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 21295.81"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Consultant Charges: 5673.00,Investigation & Lab Charges: 6808.00,Miscellaneous Charges: 2983.41,Pharmacy & Medicine Charges: 781.40,Hospital Charges: 5050.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 41000.000"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer room rent"}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Normal Room Rent Lenght of Stay : 1.00"}, {"message": "ICU Room Rent Lenght of Stay : 0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 0"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1.00"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9500.0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.092260", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100487", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Deductions not applied as it is an emergency claim"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 8366.875"}, {"message": "Copay deduction amount is 8366.875"}, {"message": "Copay deduction amount is: 8366.875"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 66935.00 and flat amount 12500 is 8366.875 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 12170.00"}, {"message": "HMOU Discount Calculated: 0"}, {"message": "MAX Discount Calculated: 12170.00"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Package: 12170.00"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "NonMedicalExpense": {"rules": [{"category": "NonMedicalExpense", "subCategory": "HandelNMEByPreAuthType", "subCategoryDisplayName": "Estimate For Initial", "reasons": [{"message": "*****Calulating CalculateNMEDeduction Started*****"}, {"message": "Matching Config Count: 1"}, {"message": "Estimate For Initial - Execution starts"}, {"message": "CalculateNMEDeduction isInitialPA: TRUE"}, {"message": "NMEDeduction percentage: 35"}, {"message": "*****Calulating CalculateNMEDeduction End*****"}, {"message": "Estimate For Initial - Execution ends"}]}], "count": 1, "category_name": "NonMedicalExpense"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :ICU"}, {"message": "Tariff ICU Room Rent From Master:0"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category ICU"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer "}, {"message": "ICU : Room Rent based on insurer "}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Normal Room Rent Lenght of Stay : 1"}, {"message": "ICU Room Rent Lenght of Stay : 1"}, {"message": "Tariff Bill Price as per insurer :0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Due to Initial PA LengthOfStay calculated on DOD - DOA "}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 1"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 0"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 9500.0"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 0"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.094212", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100488", "total_logs": 1, "claim_logs_count": 1}}, {"claimNo": *********, "logs": [{"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}, {"typeOfLog": "<PERSON><PERSON><PERSON>", "ruleEngineLogs": {"Copay": {"rules": [{"category": "Copay", "subCategory": "IntimationCopay", "subCategoryDisplayName": "Intimation", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Intimation - Execution starts"}, {"message": "Copay applicable by: PecentageOfAdmissibleAmount on each claim basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "10% of copay applied on 252799.10 and capped value is 25279.91"}, {"message": "Copay applicable based on each claim basis"}, {"message": "Copay value: 25279.91"}, {"message": "Copay deduction amount is 25279.91"}, {"message": "Copay deduction amount is: 25279.91"}, {"message": "Copay Deduction Remarks: 10% of copay applied on 252799.10 and capped value is 25279.91 (Intimation Copay)"}, {"message": "Intimation - Execution ends"}]}, {"category": "Copay", "subCategory": "RelationGrade", "subCategoryDisplayName": "Grade and Relationship", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Copay applicable by: MinOfPercentageOfAdmissibleAndCappedAmount on admission basis"}, {"message": "No copay deduction found in the historic claims"}, {"message": "Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500"}, {"message": "Copay applicable based on admission basis"}, {"message": "Copay value: 12500"}, {"message": "Copay deduction amount is 12500"}, {"message": "Copay deduction amount is: 12500"}, {"message": "Copay Deduction Remarks: Minimum capped value for copay applicable between 12.5%  of admissible amount 227519.19 and flat amount 12500 is 12500 (Mandatory Copay)"}, {"message": "Grade and Relationship - Execution ends"}]}], "count": 2, "category_name": "Copay"}, "Discount": {"rules": [{"category": "Discount", "subCategory": "Standard", "subCategoryDisplayName": "Standard", "reasons": [{"message": "*****Hospital Discount Calculation Started*****"}, {"message": "Matching Config Count: 3"}, {"message": "Standard - Execution starts"}, {"message": "All Bills are pre-post bills so overiding the bill date to DOA"}, {"message": "MMOU Discount Calculated: 26231.87"}, {"message": "HMOU Discount Calculated: 25559"}, {"message": "MAX Discount Calculated: 26231.87"}, {"message": "Discount Calculation Is Goving with HMOU"}, {"message": "Calculated Discount Amount: 0.0"}, {"message": "Calculated Discount Remarks: Hospital Charges: 6839.50,Investigation & Lab Charges: 6665.13,Miscellaneous Charges: 1197.01,Consultant Charges: 6849.76,Pharmacy & Medicine Charges: 4007.50"}, {"message": "*****HospitalDiscount Calculation Ends*****"}, {"message": "*****Calculate UCR Started*****"}, {"message": "User Selected Bill UCR Bill Amount : 0"}, {"message": "*****CalculateHospitalDiscount Calculation Ends*****"}, {"message": "Standard - Execution ends"}]}], "count": 1, "category_name": "Discount"}, "PropotinateDeduction": {"rules": [{"category": "PropotinateDeduction", "subCategory": "Standard", "subCategoryDisplayName": "Standard (IRDAI)", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Standard (IRDAI) - Execution starts"}, {"message": "Applying Excess of Tariff "}, {"message": "Calcualtion on Excess of Room Tariff Start"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Opted Room Category :SinglePrivateRoom"}, {"message": "Tariff Room Rent From Master:9500.0"}, {"message": "Tariff ICU Room Rent From Master:9500.0"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.000"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Room Rent / Nursing Excess of Tariff rates - not to be collected from the patient: 600.00"}, {"message": "Calcualtion on Excess of Room Tariff Ends"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Traiff Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Standard (IRDAI) - Execution ends"}]}, {"category": "PropotinateDeduction", "subCategory": "RoomTypeWithAilmentExclusion", "subCategoryDisplayName": "By Ward Type With Ailment Exclusion", "reasons": [{"message": "*****Calulating Eligibile Room Rent Details Category Started*****"}, {"message": "Rule Suggested Eligible Room Category PvtAC"}, {"message": "Rule Suggested Eligible Room Facility NonAC"}, {"message": "Opted Room Category SinglePrivateRoom"}, {"message": "GENERAL Category: GeneralAC,GeneralWard,Daycare,GeneralEconomyWard,MultibedWard,RehabilitationRoom"}, {"message": "SHARING Category: SharingWard,SemiPvtAC,TwinSharing,SemiDeluxeRoom,SemiPrivateRoom"}, {"message": "ICU Category: Intensive,ICU,HDU,Burnward"}, {"message": "PRIVATE Category: SingleWard,PvtAC,IsolationWard,SinglePrivateRoom"}, {"message": "DELUXE Category: Suite,Delux,DeluxeRoom"}, {"message": "Checking Under Room Master"}, {"message": "Hospital room rent master details"}, {"message": "Room category:ICU ,Room Rent PerDay:11000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Daycare ,Room Rent PerDay:2000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SemiPrivateRoom ,Room Rent PerDay:6500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:MultibedWard ,Room Rent PerDay:5500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:4500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:HDU ,Room Rent PerDay:8000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:6000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:25000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:GeneralEconomy<PERSON>ard ,Room Rent PerDay:2500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:Suite ,Room Rent PerDay:18000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:DeluxeRoom ,Room Rent PerDay:12000.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Room category:ICU ,Room Rent PerDay:10500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under General Category"}, {"message": "Eligible Room Category not found Under General Category"}, {"message": "Checking Eligible Room Category Under SHARING Category"}, {"message": "Eligible Room Category not found Under SHARING Category"}, {"message": "Checking Eligible Room Category Under ICU Category"}, {"message": "Eligible Room Category not found Under ICU Category"}, {"message": "Checking Eligible Room Category Under PRIVATE Category"}, {"message": "Eligible Room Category Found Under Private Category"}, {"message": "Found room rent details under PRIVATE Category"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking Eligible Room Category Under DELUXE Category"}, {"message": "Eligible Room Category not found Under DELUXE Category"}, {"message": "Room Rent Fount Count:1"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "Checking room rent details for GIPSA"}, {"message": "room rent details for GIPSA"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:AC"}, {"message": "After filtering data by AC/NON-AC and taking minimum price"}, {"message": "Room category:SinglePrivateRoom ,Room Rent PerDay:9500.0, Nursing Perday:0.0, Facility:0.0"}, {"message": "Calculating Room Price based on Insurer :- Starts"}, {"message": "Normal : Room Rent based on insurer nursing,room rent"}, {"message": "ICU : Room Rent based on insurer icu charges,icu monitoring charges"}, {"message": "Normal Room Rent Lenght of Stay : 5.50"}, {"message": "ICU Room Rent Lenght of Stay : 2.00"}, {"message": "Nursing Charges PerDay : 0.0"}, {"message": "RoomRent Charges PerDay : 9500.0"}, {"message": "Tariff Bill Price as per insurer :9500.0"}, {"message": "Non-Tariff bill Price as per insurer :0"}, {"message": "Total Room Price as per insurer :9500.0"}, {"message": "Minimum room rent per day calculated for GIPSA: 9500.0"}, {"message": "*****Calulating Eligibile Room Category End*****"}, {"message": "Matching Config Count: 1"}, {"message": "Room Rent limit based on amount: 9500.0"}, {"message": "Room Rent limit based on amount: -1"}, {"message": "*****Set Proportionate Calculation Started*****"}, {"message": "Calculation On Excess Of Package Starts"}, {"message": "Calculation On Excess Of Eligible Package"}, {"message": "Calculation On Excess Of Package Ends"}, {"message": "Proportionate CalCulation MaxEligibilityNormal: 9500.0"}, {"message": "Proportionate Calculation MaxEligibilityICU: -1"}, {"message": "Proportionate CalCulation Availed ICU Length Of Stay: 2.00"}, {"message": "Proportionate CalCulation Availed ICU Room Rent Per Day: 11800"}, {"message": "Proportionate CalCulation Availed Normal Length Of Stay: 5.50"}, {"message": "Proportionate CalCulation Availed Noraml Room Rent Per Day: 9499.99"}, {"message": "Proportionate CalCulation Max Normal Room Rent Allowed: 52250.000"}, {"message": "Proportionate CalCulation Proportionate Factor Normal: 100.00%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor Normal: 100%"}, {"message": "Proportionate CalCulation Max ICU Room Rent Allowed: 23600.00"}, {"message": "Proportionate CalCulation Proportionate Factor ICU: 100%"}, {"message": "Proportionate CalCulation Excess Room Rent Proportionate Factor ICU: 100%"}, {"message": "*****Proportionate Calculation End*****"}, {"message": "*****Set Proportionate Calculation Ended*****"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate deduction limit: 0"}, {"message": "Proportionate Calculation Total Admissible Amount Rs: 0"}, {"message": "By Ward Type With Ailment Exclusion - Execution ends"}]}], "count": 2, "category_name": "PropotinateDeduction"}, "Repudiation": {"rules": [{"category": "Repudiation", "subCategory": "PEDExclusion", "subCategoryDisplayName": "Waiting Period for Pre-existing Ailments", "reasons": [{"message": "Matching Config Count: 1"}, {"message": "Waiting Period for Pre-existing Ailments - Execution starts"}, {"message": "Waiting Period for Pre-existing Ailments - Execution ends"}]}], "count": 1, "category_name": "Repudiation"}}, "_will_be_processed": true}], "_cleaning_metadata": {"fields_removed": ["logCreatedDate (from logs)", "data (from logs)", "timeOfOccurrence (from data entries and rule logs)", "loggerType (from data entries and rule logs)", "configRuleLogObject (from rule engine logs)", "matchingConfig (from rule engine logs)", "preExecutionApprovedAmount (from rule engine logs)", "preExecutionTotalBillAmount (from rule engine logs)", "preExectionTotalDeductionAmount (from rule engine logs)", "postExecutionApprovedAmount (from rule engine logs)", "postExecutionTotalBillAmount (from rule engine logs)", "postExectionTotalDeductionAmount (from rule engine logs)"], "fields_renamed": ["logMessages → reasons (in rule engine logs)"], "filtering_applied": ["Only rule engine logs with non-empty matchingConfig arrays are included", "Only rule engine logs with non-zero postExecutionTotalBillAmount are included"], "grouping_applied": ["Rule engine logs grouped by category as dictionary structure with metadata support"], "cleaning_timestamp": "2025-06-14T00:20:17.098203", "purpose": "Remove unnecessary fields, rename for clarity, filter for relevant rule logs, and group by category"}, "_processing_metadata": {"filtered_for_claim_logs_only": true, "processing_timestamp": "2025-06-14T00:20:17.100491", "total_logs": 2, "claim_logs_count": 2}}], "BasicClaimInformation": {"domiDenialReason": "approved", "isClaimLocked": false, "isTechError": false, "isSuspected": false, "isInwarded": true, "eventId": *********, "insurerClmRefNo": "TP00392000024902992137", "infoReqSt1Date": "/Date(-**************)/", "infoReqSt2Date": "/Date(-**************)/", "docRecDate": "/Date(-**************+0553)/", "appDate": "/Date(-**************)/", "settlementDate": "/Date(1740940200000+0530)/", "actualReceivedDate": "/Date(1737725574000+0530)/", "denialDate": "/Date(-**************)/", "closedDate": "/Date(-**************)/", "clsDate": "/Date(-**************)/", "netPayAmount": "2,13,235", "taxAmount": "23,693", "chequeAmount": "2,13,235", "serviceTaxAmt": "0", "admissibleAmt": "0", "approverRemarks": " Left ventricular failure", "id": *********, "typeDesc": "Cashless", "type": 3, "statusName": "Settled", "statusId": 7, "receivedDate": "/Date(1737725574000+0530)/", "preAuths": "*********, *********, *********, *********, *********, *********, *********", "compRefNo": "11477261", "irDate": "/Date(-**************)/", "settledDate": "/Date(-**************)/", "latestAuditDate": "/Date(-**************)/", "doa": "/Date(*************+0530)/", "dod": "/Date(*************+0530)/", "doctorName": "<PERSON><PERSON> Dr. <PERSON>. <PERSON>          ", "doctorRegno": "", "lengthofStay": 8, "amount": 294658, "approvedAmt": 236928, "paApprovedAmt": "2,41,543", "varianceAmt": "₹2,13,235", "contactNumber": "99******07", "unMaskContactNumber": "**********", "mainNumber": *********, "claimRegionId": 14, "reciptdate": "01-Jan-0001", "epd": 0, "roomCategory": "Single private room", "accountVerified": "Account verified", "inwardNo": ",XAP*********", "lastDocRecdate": "/Date(*************+0530)/", "lastScanDocRecdate": "/Date(*************+0530)/", "latestPreAuthStatusId": 0, "roomCategoryId": 26, "isClaimLCM": 0, "clmSourceName": "Hospital Portal", "claimMainClaim": 0, "claimExternalStatus": "<PERSON><PERSON><PERSON>", "claimInsurerStatus": "<PERSON><PERSON><PERSON>", "clmOtherTreatmentDetails": "{\"HealthCareSystem\":\"Allopathy\",\"PrimaryApproach\":\"\",\"PrimaryPosition\":\"\",\"PrimaryAilmentDuration\":\"8\",\"SecondaryApproach\":null,\"SecondaryPosition\":null,\"SecondaryAilmentDuration\":null,\"TertiaryApproach\":null,\"TertiaryPosition\":null,\"TertiaryAilmentDuration\":null,\"EligibleNormalRoomRent\":9500,\"EligibleICURoomRent\":0,\"IsRuleConfigured\":true,\"IsEmergency\":false,\"IsDigitized\":true}", "clmEligibleRoomCategory": 3, "eligibleRoomType": "Single Ward ( Private / Special / Executive Ward)", "clmTotalCostofCare": 294658, "clmNetClaimedAmt": 294658, "clmHospDiscountOnBill": 0, "chequeAmount_Claim": "", "chequeAmount_Interest": ""}, "AilmentSummary": {"code": "I50.1", "group": "", "treatmentName": "Conservative Management", "descs": "Left ventricular failure", "summary": ".Left ventricular failure", "ids": "11181", "ailmentBodySystem": "Cardiovascular System"}, "HospitalDetails": {"id": 95748, "name": "Fortis Hospital", "cityId": 0, "city": "New Delhi", "stateId": 0, "state": "Delhi", "pincode": "110088", "isPPN": "GIPSA", "typeOfCare": "Tertiary", "hospTariffURL": "0", "hospGeneralTariffURL": "0", "hospisVerified": true}, "PatientAndPolicyDetails": {"policyHeader": {"polId": 62633282, "polCorpId": 1006639, "groupCorpId": 1049400, "polNo": "92000034240400000025", "polDevelopmentOfficer": "", "polDevelopmentAgent": "", "polInsCompanyID": 1, "polTypeID": 1, "polSubTypeID": 100, "polInsCompanyName": "The New India Assurance Co. Ltd", "polStartDate": "/Date(1711909800000+0530)/", "polEndDate": "/Date(1743359400000+0530)/", "polHldrName": "Tata Consultancy Services Ltd", "isFloater": false, "isRetail": false, "isMixed": false, "isActive": false, "polFloaterType": "Non Floater", "polTPAId": 93, "polSumInsured": "0", "polPaymentDirect": false, "polBlackListed": false, "maServicingBranchId": "2", "polTotalPremium": 0, "isPartialPending": false, "polRenewalRating": 0, "polCategoryId": 0, "polDeleteStatus": false, "tpaName": "Medi Assist Insurance TPA Pvt. Ltd.", "policySequence": 0, "isSuccess": false}, "mediassistId": **********, "active": true, "name": "<PERSON><PERSON>", "contactNo": "99******07", "unMaskContactNo": "**********", "sex": "M", "dob": "/Date(-************+0530)/", "age": 84, "relId": 5, "relName": "Father", "occId": 0, "cumBonusPer": 0, "domiLimit": 12000, "premium": 0, "balanceSI": 49279, "sumInsured": 300000, "bsi": 49279, "totalSumInsured": 0, "amtSpent": 250721, "amtAuthorised": 0, "wef": "01-Apr-2024", "areaCode": "New Delhi", "email": "vi**********@tcs.com", "addedOn": "/Date(-**************)/", "addedBy": 0, "niaPersonID": "MEMBER412857", "modifiedUser": 0, "modifiedOn": "/Date(-**************)/", "benefUserId": 128239305, "unmaskedEmail": "<EMAIL>"}, "MissingDocuments": [], "_final_structure_metadata": {"structure_applied": ["DeductionsInClaim moved to top level (first priority)", "logs mapped to \"Deduction reasons\"", "claimInfo mapped to \"BasicClaimInformation\"", "ailmentSmry mapped to \"AilmentSummary\"", "hospHeader mapped to \"HospitalDetails\"", "benefDtls mapped to \"PatientAndPolicyDetails\"", "missingDocuments mapped to \"MissingDocuments\""], "final_structure_keys": ["DeductionsInClaim", "Deduction reasons", "BasicClaimInformation", "<PERSON><PERSON>ent<PERSON>ummar<PERSON>", "HospitalDetails", "PatientAndPolicyDetails"], "restructuring_timestamp": "2025-06-14T00:20:17.101462", "purpose": "Create top-level dictionary structure with business-friendly key names"}}