import google.genai as genai
from typing import List, Dict, Any, Optional
import logging
import json
import os
from config import settings
from vector_store import VectorStore

logger = logging.getLogger(__name__)

class QAEngine:
    def __init__(self):
        self.vector_store = VectorStore()
        self.client = genai.Client(api_key=settings.GEMINI_API_KEY)
        self.model_name = settings.GEMINI_MODEL
        self.max_tokens = settings.MAX_TOKENS
    
    def _create_context_from_chunks(self, chunks: List[Dict[str, Any]]) -> str:
        """Create patient-focused context string from retrieved chunks"""
        if not chunks:
            return "No relevant information found about your claim."

        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            content = chunk.get('content', '')
            chunk_type = chunk.get('chunk_type', 'unknown')
            claim_no = chunk.get('claim_no', 'unknown')

            # Use patient-friendly labels
            type_labels = {
                'rule_engine_log': 'Benefit Processing',
                'general_log': 'Claim Activity',
                'claim_summary': 'Claim Overview'
            }
            friendly_type = type_labels.get(chunk_type, chunk_type)

            context_parts.append(f"--- Information {i} (Claim #{claim_no} - {friendly_type}) ---")
            context_parts.append(content)
            context_parts.append("")

        return "\n".join(context_parts)
    
    def _create_system_instruction(self) -> str:
        """Create patient-focused system instruction for Gemini"""
        return """You are a helpful insurance claims assistant who explains claim processing information from the patient's perspective.

Your role is to:
1. Help patients understand their insurance claim status, approvals, and benefit decisions
2. Explain what benefits were approved, what amounts were covered, and any deductions applied
3. Clarify why certain policy rules affected their claim outcome
4. Provide clear explanations about coverage decisions and financial impacts
5. Focus on what the patient needs to know about their claim

Important Guidelines:
- Always respond from the patient's perspective - explain what happened to THEIR claim
- Use simple, patient-friendly language and avoid technical insurance jargon
- Focus on outcomes that matter to the patient: approved amounts, coverage decisions, deductions
- Explain policy rules in terms of how they affected the patient's benefits
- Never discuss internal system processes, technical operations, or backend processing details
- If asked about system internals, redirect to patient-relevant information instead
- Highlight important financial information clearly (what was approved, what was deducted, final amounts)
- Use bullet points and clear formatting to make information easy to understand

Always base your answers strictly on the provided claim data and focus on patient-relevant outcomes."""
    
    def _create_user_prompt(self, question: str, context: str) -> str:
        """Create patient-focused user prompt with question and context"""
        return f"""Based on the following information about the patient's insurance claim, please answer their question:

PATIENT'S CLAIM INFORMATION:
{context}

PATIENT'S QUESTION: {question}

Please provide a clear, patient-friendly answer that explains:
- What happened with their claim
- What benefits were approved or denied
- What amounts they can expect
- Why certain decisions were made based on their policy
- Any next steps they should be aware of

Focus only on information that is relevant and helpful to the patient. Avoid discussing internal system processes."""
    
    def answer_question(self, question: str, claim_no: Optional[int] = None,
                       max_results: int = 5) -> Dict[str, Any]:
        """Answer a question based on the claims data using full context for claim_no or vector search otherwise"""
        try:
            # If claim_no is provided, get entire context for that claim
            if claim_no:
                return self._answer_with_full_context(question, claim_no)

            # Otherwise, use vector search as before
            relevant_chunks = self.vector_store.search_similar(
                query=question,
                claim_no=claim_no,
                max_results=max_results
            )

            if not relevant_chunks:
                return {
                    "question": question,
                    "answer": "I couldn't find any relevant information in the claims data to answer your question. Please make sure the claim data has been processed and stored.",
                    "relevant_chunks": [],
                    "confidence_score": 0.0
                }

            # Create context from chunks
            context = self._create_context_from_chunks(relevant_chunks)

            # Create prompt for Gemini
            system_instruction = self._create_system_instruction()
            user_prompt = self._create_user_prompt(question, context)

            # Call Gemini API with new client
            from google.genai import types
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=user_prompt,
                config=types.GenerateContentConfig(
                    system_instruction=system_instruction,
                    max_output_tokens=self.max_tokens,
                    temperature=0.1,
                    top_p=0.9
                )
            )

            answer = response.text.strip()

            # Calculate confidence score based on similarity scores
            avg_similarity = sum(chunk.get('similarity_score', 0) for chunk in relevant_chunks) / len(relevant_chunks)

            # Prepare chunk summaries for response
            chunk_summaries = []
            for chunk in relevant_chunks:
                chunk_summaries.append({
                    "claim_no": chunk.get('claim_no'),
                    "chunk_type": chunk.get('chunk_type'),
                    "similarity_score": chunk.get('similarity_score'),
                    "preview": chunk.get('content', '')[:200] + "..." if len(chunk.get('content', '')) > 200 else chunk.get('content', '')
                })

            return {
                "question": question,
                "answer": answer,
                "relevant_chunks": chunk_summaries,
                "confidence_score": avg_similarity
            }

        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "relevant_chunks": [],
                "confidence_score": 0.0
            }

    def _answer_with_full_context(self, question: str, claim_no: int) -> Dict[str, Any]:
        """Answer a question using optimized context for a specific claim"""
        try:
            # Get all chunks for the claim
            all_chunks = self.vector_store.get_claim_chunks(claim_no)

            if not all_chunks:
                return {
                    "question": question,
                    "answer": f"I couldn't find any data for claim number {claim_no}. Please make sure the claim data has been processed and stored.",
                    "relevant_chunks": [],
                    "confidence_score": 0.0
                }

            # Optimize chunks for better context
            optimized_chunks = self._optimize_chunks_for_context(all_chunks, question)

            # Create context from optimized chunks
            context = self._create_context_from_chunks(optimized_chunks)

            # Create prompt for Gemini
            system_instruction = self._create_system_instruction()
            user_prompt = self._create_user_prompt(question, context)

            # Call Gemini API with new client
            from google.genai import types
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=user_prompt,
                config=types.GenerateContentConfig(
                    system_instruction=system_instruction,
                    max_output_tokens=self.max_tokens,
                    temperature=0.1,
                    top_p=0.9
                )
            )

            answer = response.text.strip()

            # For full context, confidence is high since we have all available data
            confidence_score = 1.0

            # Prepare chunk summaries for response
            chunk_summaries = []
            for chunk in optimized_chunks:
                chunk_summaries.append({
                    "claim_no": chunk.get('claim_no'),
                    "chunk_type": chunk.get('chunk_type'),
                    "similarity_score": 1.0,  # Full context, so all chunks are equally relevant
                    "preview": chunk.get('content', '')[:200] + "..." if len(chunk.get('content', '')) > 200 else chunk.get('content', '')
                })

            return {
                "question": question,
                "answer": answer,
                "relevant_chunks": chunk_summaries,
                "confidence_score": confidence_score
            }

        except Exception as e:
            logger.error(f"Error answering question with full context: {str(e)}")
            return {
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "relevant_chunks": [],
                "confidence_score": 0.0
            }

    def _optimize_chunks_for_context(self, all_chunks: List[Dict[str, Any]], question: str) -> List[Dict[str, Any]]:
        """Optimize chunks for better context while reducing token count"""

        # Separate structured and granular chunks
        structured_chunks = []
        granular_chunks = []

        for chunk in all_chunks:
            chunk_type = chunk.get('chunk_type', '')
            if chunk_type.endswith('_structured'):
                structured_chunks.append(chunk)
            else:
                granular_chunks.append(chunk)

        logger.info(f"Found {len(structured_chunks)} structured chunks and {len(granular_chunks)} granular chunks")

        # Strategy 1: Always include structured chunks (they contain comprehensive section data)
        optimized_chunks = structured_chunks.copy()

        # Strategy 2: Add relevant granular chunks based on question keywords
        question_lower = question.lower()
        relevant_keywords = {
            'deduction': ['deduction', 'copay', 'discount', 'reduction', 'charge'],
            'financial': ['amount', 'cost', 'price', 'money', 'payment', 'bill', 'approved'],
            'medical': ['treatment', 'medical', 'doctor', 'hospital', 'procedure', 'diagnosis'],
            'policy': ['policy', 'coverage', 'benefit', 'limit', 'exclusion', 'waiting'],
            'rule': ['rule', 'why', 'reason', 'because', 'applied', 'calculation']
        }

        # Find relevant categories based on question
        relevant_categories = []
        for category, keywords in relevant_keywords.items():
            if any(keyword in question_lower for keyword in keywords):
                relevant_categories.append(category)

        # If no specific categories found, include financial and deduction by default
        if not relevant_categories:
            relevant_categories = ['financial', 'deduction']

        logger.info(f"Question categories identified: {relevant_categories}")

        # Add relevant granular chunks
        added_granular = 0
        max_granular_chunks = 20  # Limit granular chunks

        for chunk in granular_chunks:
            if added_granular >= max_granular_chunks:
                break

            chunk_metadata = chunk.get('metadata', {})
            chunk_category = chunk_metadata.get('category', '')
            chunk_content = chunk.get('content', '').lower()

            # Include chunk if it matches relevant categories or contains question keywords
            should_include = False

            # Check category match
            if chunk_category in relevant_categories:
                should_include = True

            # Check content relevance
            if not should_include:
                for category in relevant_categories:
                    if category in relevant_keywords:
                        if any(keyword in chunk_content for keyword in relevant_keywords[category]):
                            should_include = True
                            break

            if should_include:
                optimized_chunks.append(chunk)
                added_granular += 1

        logger.info(f"Optimized chunks: {len(structured_chunks)} structured + {added_granular} granular = {len(optimized_chunks)} total")

        return optimized_chunks

    def get_claim_summary(self, claim_no: int) -> Dict[str, Any]:
        """Get a comprehensive summary of a specific claim"""
        try:
            # Get all chunks for the claim
            claim_chunks = self.vector_store.get_claim_chunks(claim_no)
            
            if not claim_chunks:
                return {
                    "claim_no": claim_no,
                    "summary": f"No data found for claim number {claim_no}",
                    "details": {}
                }
            
            # Create context from all chunks
            context = self._create_context_from_chunks(claim_chunks)
            
            # Create summary prompt
            summary_question = f"Please provide a comprehensive summary of claim {claim_no}, including key decisions, financial impacts, and important rule applications."

            system_instruction = self._create_system_instruction()
            user_prompt = self._create_user_prompt(summary_question, context)

            # Call Gemini API with new client
            from google.genai import types
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=user_prompt,
                config=types.GenerateContentConfig(
                    system_instruction=system_instruction,
                    max_output_tokens=self.max_tokens,
                    temperature=0.1
                )
            )

            summary = response.text.strip()
            
            # Analyze chunk types and counts
            chunk_analysis = {}
            for chunk in claim_chunks:
                chunk_type = chunk.get('chunk_type', 'unknown')
                chunk_analysis[chunk_type] = chunk_analysis.get(chunk_type, 0) + 1
            
            return {
                "claim_no": claim_no,
                "summary": summary,
                "details": {
                    "total_chunks": len(claim_chunks),
                    "chunk_types": chunk_analysis,
                    "data_available": True
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating claim summary: {str(e)}")
            return {
                "claim_no": claim_no,
                "summary": f"Error generating summary: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def suggest_questions(self, claim_no: Optional[int] = None) -> List[str]:
        """Suggest patient-relevant questions based on available data"""
        base_questions = [
            "How much was approved for my claim?",
            "What deductions were applied to my claim and why?",
            "What benefits were covered under my policy?",
            "What was the final amount I will receive?",
            "Were there any policy restrictions that affected my claim?",
            "What medical expenses were covered?",
            "What non-medical expenses were approved?",
            "Why was my claim amount different from what I expected?",
            "Are there any waiting period restrictions on my policy?",
            "What network restrictions applied to my claim?"
        ]
        
        if claim_no:
            # Get claim-specific data to suggest more targeted questions
            try:
                claim_chunks = self.vector_store.get_claim_chunks(claim_no)
                if claim_chunks:
                    # Analyze available data to suggest specific questions
                    categories = set()
                    has_financial_data = False
                    
                    for chunk in claim_chunks:
                        metadata = chunk.get('metadata', {})
                        if metadata.get('category'):
                            categories.add(metadata['category'])
                        if metadata.get('has_financial_impact'):
                            has_financial_data = True
                    
                    specific_questions = []
                    for category in categories:
                        specific_questions.append(f"What {category} rules were applied to claim {claim_no}?")
                    
                    if has_financial_data:
                        specific_questions.append(f"What was the detailed financial breakdown for claim {claim_no}?")
                    
                    return specific_questions + base_questions[:5]
            except:
                pass
        
        return base_questions

    def get_claim_context(self, claim_no: int) -> Dict[str, Any]:
        """Get entire context/data for a specific claim without LLM processing"""
        try:
            # Get all chunks for the claim
            claim_chunks = self.vector_store.get_claim_chunks(claim_no)

            if not claim_chunks:
                return {
                    "claim_no": claim_no,
                    "message": f"No data found for claim number {claim_no}",
                    "relevant_chunks": [],
                    "context_data": {},
                    "total_chunks": 0
                }

            # Organize chunks by type for better structure
            organized_chunks = {}
            chunk_summaries = []

            for chunk in claim_chunks:
                chunk_type = chunk.get('chunk_type', 'unknown')
                if chunk_type not in organized_chunks:
                    organized_chunks[chunk_type] = []

                organized_chunks[chunk_type].append({
                    "content": chunk.get('content', ''),
                    "metadata": chunk.get('metadata', {})
                })

                # Create summary for response
                chunk_summaries.append({
                    "claim_no": chunk.get('claim_no'),
                    "chunk_type": chunk_type,
                    "preview": chunk.get('content', '')[:200] + "..." if len(chunk.get('content', '')) > 200 else chunk.get('content', ''),
                    "metadata": chunk.get('metadata', {})
                })

            # Create structured context data
            context_data = {
                "claim_number": claim_no,
                "chunks_by_type": organized_chunks,
                "chunk_type_counts": {chunk_type: len(chunks) for chunk_type, chunks in organized_chunks.items()},
                "total_chunks": len(claim_chunks),
                "available_data_types": list(organized_chunks.keys())
            }

            return {
                "claim_no": claim_no,
                "message": f"Retrieved complete context for claim {claim_no}",
                "relevant_chunks": chunk_summaries,
                "context_data": context_data,
                "total_chunks": len(claim_chunks)
            }

        except Exception as e:
            logger.error(f"Error retrieving claim context: {str(e)}")
            return {
                "claim_no": claim_no,
                "message": f"Error retrieving context for claim {claim_no}: {str(e)}",
                "relevant_chunks": [],
                "context_data": {"error": str(e)},
                "total_chunks": 0
            }
