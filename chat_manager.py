#!/usr/bin/env python3
"""
Chat Session Manager for ClaimGPT
Handles conversation memory, user context, and session management
"""

import uuid
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from models import ChatSession, ChatMessage, ChatRequest, ChatResponse
from vector_store import VectorStore

logger = logging.getLogger(__name__)

class ChatSessionManager:
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.vector_store = VectorStore()
        self.session_timeout_hours = 24  # Sessions expire after 24 hours
        
    def create_session(self, claim_no: Optional[int] = None) -> str:
        """Create a new chat session"""
        session_id = str(uuid.uuid4())
        
        # Get user name from vector DB if claim_no provided
        user_name = None
        if claim_no:
            user_name = self._get_user_name_from_claim(claim_no)
        
        session = ChatSession(
            session_id=session_id,
            user_name=user_name,
            claim_no=claim_no
        )
        
        self.sessions[session_id] = session
        logger.info(f"Created new chat session {session_id} for claim {claim_no}, user: {user_name}")
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get existing session or None if not found/expired"""
        if session_id not in self.sessions:
            return None
            
        session = self.sessions[session_id]
        
        # Check if session is expired
        last_activity = datetime.fromisoformat(session.last_activity)
        if datetime.now() - last_activity > timedelta(hours=self.session_timeout_hours):
            self._cleanup_session(session_id)
            return None
            
        return session
    
    def add_message(self, session_id: str, role: str, content: str, claim_no: Optional[int] = None) -> bool:
        """Add a message to the session"""
        session = self.get_session(session_id)
        if not session:
            return False
            
        message = ChatMessage(
            role=role,
            content=content,
            claim_no=claim_no
        )
        
        session.messages.append(message)
        session.last_activity = datetime.now().isoformat()
        
        # Update claim_no if provided and not already set
        if claim_no and not session.claim_no:
            session.claim_no = claim_no
            # Try to get user name if we didn't have it before
            if not session.user_name:
                session.user_name = self._get_user_name_from_claim(claim_no)
        
        return True
    
    def get_conversation_history(self, session_id: str, max_messages: int = 10) -> List[ChatMessage]:
        """Get recent conversation history for context"""
        session = self.get_session(session_id)
        if not session:
            return []
            
        # Return last N messages for context
        return session.messages[-max_messages:] if session.messages else []
    
    def generate_introduction(self, session: ChatSession) -> str:
        """Generate personalized introduction message"""
        if session.user_name and session.claim_no:
            return f"Hi {session.user_name}! I'm here to help you understand your insurance claim {session.claim_no}. What would you like to know about your claim today?"
        elif session.user_name:
            return f"Hi {session.user_name}! I'm here to help you understand your insurance claims. Do you have a specific claim number you'd like to discuss?"
        elif session.claim_no:
            return f"Hi there! I'm here to help you understand your insurance claim {session.claim_no}. What would you like to know about your claim today?"
        else:
            return "Hi there! I'm ClaimGPT, your personal insurance claims assistant. I'm here to help you understand your insurance claims in simple, easy-to-understand terms. Do you have a claim number you'd like to discuss?"
    
    def _get_user_name_from_claim(self, claim_no: int) -> Optional[str]:
        """Extract user name from claim data in vector DB"""
        try:
            # Get claim chunks
            claim_chunks = self.vector_store.get_claim_chunks(claim_no)
            
            # Look for user name in patient details or basic claim info
            for chunk in claim_chunks:
                content = chunk.get('content', '').lower()
                chunk_type = chunk.get('chunk_type', '')
                
                # Check if this chunk contains patient information
                if 'patient' in chunk_type or 'member' in content or 'patient' in content:
                    # Try to extract name from content
                    lines = chunk.get('content', '').split('\n')
                    for line in lines:
                        if 'membername' in line.lower() or 'patient name' in line.lower() or 'name:' in line.lower():
                            # Extract name after colon
                            if ':' in line:
                                name = line.split(':', 1)[1].strip()
                                # Clean up the name (remove quotes, extra spaces)
                                name = name.strip('"\'').strip()
                                if name and len(name) > 1:
                                    logger.info(f"Found user name: {name} for claim {claim_no}")
                                    return name
            
            logger.info(f"No user name found for claim {claim_no}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting user name for claim {claim_no}: {str(e)}")
            return None
    
    def _cleanup_session(self, session_id: str):
        """Remove expired session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Cleaned up expired session {session_id}")
    
    def cleanup_expired_sessions(self):
        """Clean up all expired sessions"""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            last_activity = datetime.fromisoformat(session.last_activity)
            if current_time - last_activity > timedelta(hours=self.session_timeout_hours):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self._cleanup_session(session_id)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics about active sessions"""
        active_sessions = len(self.sessions)
        sessions_with_claims = sum(1 for s in self.sessions.values() if s.claim_no)
        sessions_with_names = sum(1 for s in self.sessions.values() if s.user_name)
        
        return {
            "active_sessions": active_sessions,
            "sessions_with_claims": sessions_with_claims,
            "sessions_with_names": sessions_with_names,
            "session_timeout_hours": self.session_timeout_hours
        }

# Global chat manager instance
chat_manager = ChatSessionManager()
