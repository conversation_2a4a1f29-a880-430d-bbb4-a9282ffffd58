import chromadb
from chromadb.config import Settings as ChromaSettings
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional
import json
import logging
from config import settings
from models import ProcessedClaimChunk

logger = logging.getLogger(__name__)

class VectorStore:
    def __init__(self):
        self.client = chromadb.PersistentClient(
            path=settings.CHROMA_PERSIST_DIRECTORY,
            settings=ChromaSettings(anonymized_telemetry=False)
        )
        self.embedding_model = SentenceTransformer(settings.EMBEDDING_MODEL)
        self.collection_name = "claims_data"
        self.collection = self._get_or_create_collection()
    
    def _get_or_create_collection(self):
        """Get or create the claims data collection"""
        try:
            collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Retrieved existing collection: {self.collection_name}")
        except Exception:
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Claims processing data for Q&A"}
            )
            logger.info(f"Created new collection: {self.collection_name}")
        
        return collection
    
    def add_chunks(self, chunks: List[ProcessedClaimChunk]) -> bool:
        """Add processed claim chunks to the vector store"""
        try:
            if not chunks:
                logger.warning("No chunks provided to add")
                return False
            
            # Prepare data for ChromaDB
            documents = []
            metadatas = []
            ids = []
            
            for chunk in chunks:
                documents.append(chunk.content)
                
                # Prepare metadata (ChromaDB requires string values)
                metadata = {
                    "claim_no": str(chunk.claim_no),
                    "chunk_type": chunk.chunk_type,
                    "timestamp": chunk.timestamp,
                }
                
                # Add chunk-specific metadata as strings
                for key, value in chunk.metadata.items():
                    if isinstance(value, (list, dict)):
                        metadata[f"meta_{key}"] = json.dumps(value)
                    else:
                        metadata[f"meta_{key}"] = str(value)
                
                metadatas.append(metadata)
                ids.append(chunk.chunk_id)
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(documents).tolist()
            
            # Add to collection
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings,
                ids=ids
            )
            
            logger.info(f"Successfully added {len(chunks)} chunks to vector store")
            return True
            
        except Exception as e:
            logger.error(f"Error adding chunks to vector store: {str(e)}")
            return False
    
    def search_similar(self, query: str, claim_no: Optional[int] = None, 
                      max_results: int = 5) -> List[Dict[str, Any]]:
        """Search for similar chunks based on query"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query]).tolist()[0]
            
            # Prepare where clause for filtering
            where_clause = {}
            if claim_no:
                where_clause["claim_no"] = str(claim_no)
            
            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results,
                where=where_clause if where_clause else None,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                    distance = results['distances'][0][i] if results['distances'] else 0
                    
                    # Convert metadata back to proper types
                    processed_metadata = {}
                    for key, value in metadata.items():
                        if key.startswith('meta_'):
                            original_key = key[5:]  # Remove 'meta_' prefix
                            try:
                                # Try to parse JSON for complex types
                                if value.startswith('[') or value.startswith('{'):
                                    processed_metadata[original_key] = json.loads(value)
                                else:
                                    processed_metadata[original_key] = value
                            except:
                                processed_metadata[original_key] = value
                        else:
                            processed_metadata[key] = value
                    
                    formatted_results.append({
                        "content": doc,
                        "metadata": processed_metadata,
                        "similarity_score": 1 - distance,  # Convert distance to similarity
                        "claim_no": metadata.get("claim_no"),
                        "chunk_type": metadata.get("chunk_type")
                    })
            
            logger.info(f"Found {len(formatted_results)} similar chunks for query")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            return []
    
    def get_claim_chunks(self, claim_no: int) -> List[Dict[str, Any]]:
        """Get all chunks for a specific claim"""
        try:
            results = self.collection.get(
                where={"claim_no": str(claim_no)},
                include=["documents", "metadatas"]
            )
            
            formatted_results = []
            if results['documents']:
                for i, doc in enumerate(results['documents']):
                    metadata = results['metadatas'][i] if results['metadatas'] else {}
                    
                    # Convert metadata back to proper types
                    processed_metadata = {}
                    for key, value in metadata.items():
                        if key.startswith('meta_'):
                            original_key = key[5:]
                            try:
                                if value.startswith('[') or value.startswith('{'):
                                    processed_metadata[original_key] = json.loads(value)
                                else:
                                    processed_metadata[original_key] = value
                            except:
                                processed_metadata[original_key] = value
                        else:
                            processed_metadata[key] = value
                    
                    formatted_results.append({
                        "content": doc,
                        "metadata": processed_metadata,
                        "claim_no": metadata.get("claim_no"),
                        "chunk_type": metadata.get("chunk_type")
                    })
            
            logger.info(f"Retrieved {len(formatted_results)} chunks for claim {claim_no}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error retrieving claim chunks: {str(e)}")
            return []
    
    def delete_claim_data(self, claim_no: int) -> bool:
        """Delete all data for a specific claim"""
        try:
            # Get all IDs for the claim
            results = self.collection.get(
                where={"claim_no": str(claim_no)},
                include=["documents"]
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} chunks for claim {claim_no}")
                return True
            else:
                logger.info(f"No chunks found for claim {claim_no}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting claim data: {str(e)}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the collection"""
        try:
            count = self.collection.count()
            
            # Get sample of metadata to understand data distribution
            sample_results = self.collection.get(
                limit=100,
                include=["metadatas"]
            )
            
            claim_nos = set()
            chunk_types = {}
            
            if sample_results['metadatas']:
                for metadata in sample_results['metadatas']:
                    claim_no = metadata.get('claim_no')
                    if claim_no:
                        claim_nos.add(claim_no)
                    
                    chunk_type = metadata.get('chunk_type', 'unknown')
                    chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
            
            return {
                "total_chunks": count,
                "unique_claims": len(claim_nos),
                "chunk_types": chunk_types,
                "sample_claim_numbers": list(claim_nos)[:10]  # First 10 claim numbers
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            return {"error": str(e)}
