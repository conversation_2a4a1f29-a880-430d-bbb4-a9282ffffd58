from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

class LogMessage(BaseModel):
    timeOfOccurrence: str
    message: str
    loggerType: str

class ConfigRuleLogObject(BaseModel):
    displayName: str
    value: str
    isFilterApplicable: bool
    defaultValue: str
    isToBeDisplayed: bool
    claimValue: Optional[str] = None

class RuleEngineLog(BaseModel):
    timeOfOccurrence: str
    category: str
    subCategory: str
    subCategoryDisplayName: str
    loggerType: str
    logMessages: List[LogMessage] = []
    matchingConfig: List[Dict[str, Any]] = []
    preExecutionApprovedAmount: float
    preExecutionTotalBillAmount: float
    preExectionTotalDeductionAmount: float
    postExecutionApprovedAmount: float
    postExecutionTotalBillAmount: float
    postExectionTotalDeductionAmount: float
    configRuleLogObject: List[ConfigRuleLogObject] = []

class LogData(BaseModel):
    timeOfOccurrence: str
    message: str
    loggerType: str

class ClaimLog(BaseModel):
    data: List[LogData] = []
    logCreatedDate: str
    typeOfLog: str
    ruleEngineLogs: List[RuleEngineLog] = []

class ClaimData(BaseModel):
    claimNo: int
    logs: List[ClaimLog]

class ClaimsResponse(BaseModel):
    data: Optional[List[ClaimData]] = None  # For old format with 'data' wrapper

    @classmethod
    def from_response(cls, response_data: Union[Dict[str, Any], List[Dict[str, Any]]]):
        """Create ClaimsResponse from either old format (with 'data' wrapper) or new format (direct array)"""
        if isinstance(response_data, list):
            # New format: direct array of claim objects
            claim_data_list = [ClaimData(**claim) for claim in response_data]
            return cls(data=claim_data_list)
        elif isinstance(response_data, dict) and 'data' in response_data:
            # Old format: wrapped in 'data' property
            return cls(**response_data)
        else:
            raise ValueError("Invalid response format: expected either a list of claims or a dict with 'data' property")

    def get_claims(self) -> List[ClaimData]:
        """Get the list of claims regardless of the input format"""
        return self.data or []

class ProcessedClaimChunk(BaseModel):
    claim_no: int
    chunk_id: str
    chunk_type: str
    content: str
    metadata: Dict[str, Any]
    timestamp: str

class QuestionRequest(BaseModel):
    question: str = Field(..., description="Question to ask about the claims data (mandatory)")
    claim_no: Optional[int] = None
    max_results: Optional[int] = Field(default=5, ge=1, le=20)
    context_only: Optional[bool] = Field(default=False, description="If True, returns entire context without LLM processing")

class QuestionResponse(BaseModel):
    question: str  # Always provided since question is mandatory
    answer: str    # Always provided since LLM will always generate an answer
    relevant_chunks: List[Dict[str, Any]]
    confidence_score: float
    claim_no: Optional[int] = None  # Added for context-only responses
    context_data: Optional[Dict[str, Any]] = None  # Added for structured context data

class FetchClaimsRequest(BaseModel):
    claim_numbers: List[int] = Field(..., min_items=1, max_items=50)
    login_name: Optional[str] = Field(default="avnish.garg")
    login_email: Optional[str] = Field(default="<EMAIL>")
    login_user_id: Optional[int] = Field(default=18608)

class FetchSingleClaimRequest(BaseModel):
    claim_number: int = Field(..., description="Single claim number to process")
    login_name: Optional[str] = Field(default="avnish.garg")
    login_email: Optional[str] = Field(default="<EMAIL>")
    login_user_id: Optional[int] = Field(default=18608)
