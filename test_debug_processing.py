#!/usr/bin/env python3
"""
Test script to process debug data and test the /ask endpoint
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_health():
    """Test if API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✓ API is healthy and running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def process_debug_data():
    """Process the debug data file"""
    print("Processing debug data...")
    
    try:
        response = requests.post(f"{BASE_URL}/process-debug-data")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Debug data processed successfully")
            print(f"  Main claim number: {result['main_claim_no']}")
            print(f"  Chunks created: {result['chunks_created']}")
            print(f"  Message: {result['message']}")
            return result['main_claim_no']
        else:
            print(f"❌ Failed to process debug data: {response.status_code}")
            print(f"  Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error processing debug data: {e}")
        return None

def test_ask_with_claim_no(claim_no):
    """Test /ask endpoint with the processed claim number"""
    print(f"\nTesting /ask endpoint with claim {claim_no}...")
    
    questions = [
        "What was the total approved amount for this claim?",
        "What deductions were applied to this claim?",
        "What was the patient's copay amount?",
        "What hospital discount was applied?",
        "What was the final settlement amount?"
    ]
    
    for question in questions:
        print(f"\nAsking: {question}")
        
        try:
            response = requests.post(f"{BASE_URL}/ask", json={
                "question": question,
                "claim_no": claim_no,
                "max_results": 5
            })
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ Answer: {result['answer'][:200]}...")
                print(f"  Confidence: {result['confidence_score']}")
                print(f"  Chunks used: {len(result['relevant_chunks'])}")
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error asking question: {e}")
        
        time.sleep(1)  # Rate limiting

def test_ask_without_claim_no():
    """Test /ask endpoint without claim number (should use vector search)"""
    print(f"\nTesting /ask endpoint without claim number...")
    
    try:
        response = requests.post(f"{BASE_URL}/ask", json={
            "question": "What deductions were applied?",
            "max_results": 3
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Answer: {result['answer'][:200]}...")
            print(f"  Confidence: {result['confidence_score']}")
            print(f"  Chunks used: {len(result['relevant_chunks'])}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error asking question: {e}")

def test_stats():
    """Test database stats"""
    print(f"\nTesting database stats...")
    
    try:
        response = requests.get(f"{BASE_URL}/stats")
        
        if response.status_code == 200:
            result = response.json()
            stats = result.get('database_stats', {})
            print(f"✓ Database stats:")
            print(f"  Total chunks: {stats.get('total_chunks', 0)}")
            print(f"  Total claims: {stats.get('total_claims', 0)}")
        else:
            print(f"❌ Error getting stats: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting stats: {e}")

def main():
    """Main test function"""
    print("Testing Debug Data Processing and /ask Endpoint")
    print("=" * 50)
    
    # Test if API is running
    if not test_health():
        print("Please start the API server first: uvicorn main:app --reload --port 8001")
        return
    
    # Process debug data
    claim_no = process_debug_data()
    if not claim_no:
        print("Failed to process debug data. Cannot continue with tests.")
        return
    
    # Wait a moment for processing
    time.sleep(2)
    
    # Test database stats
    test_stats()
    
    # Test /ask endpoint with claim number (full context)
    test_ask_with_claim_no(claim_no)
    
    # Test /ask endpoint without claim number (vector search)
    test_ask_without_claim_no()
    
    print("\n" + "=" * 50)
    print("Tests completed!")
    print(f"Debug data has been processed for claim {claim_no}")
    print("You can now use the /ask endpoint with this claim number to get full context responses.")

if __name__ == "__main__":
    main()
