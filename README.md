# Claims Data Q&A API

A FastAPI application for processing large insurance claims data (244KB+ JSON) and providing intelligent Q&A capabilities using vector storage and LLM integration.

## Features

- **Data Processing**: Reformats complex claims JSON into LLM-friendly chunks
- **Vector Storage**: Uses ChromaDB for efficient similarity search
- **LLM Integration**: Google Gemini for intelligent question answering (cheaper than OpenAI)
- **FastAPI Backend**: RESTful API with automatic documentation
- **Contextual Retrieval**: Fetches only relevant context to reduce token usage
- **Claims Analysis**: Detailed financial impact and rule analysis

## Architecture

```
User Input → API → Data Processor → Vector DB → Context Retrieval → LLM → Response
```

1. **API receives large JSON** (244KB claims data)
2. **Data Processor** reformats into structured, readable chunks
3. **Vector Store** creates embeddings and stores chunks
4. **Q&A Engine** retrieves relevant context and queries LLM
5. **Response** provides accurate answers with source references

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:

```env
# Google Gemini (cheaper than OpenAI)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash

# Vector Database
CHROMA_PERSIST_DIRECTORY=./chroma_db
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Application
MAX_TOKENS=4000
API_HOST=0.0.0.0
API_PORT=8001

# Proclaim API (for fetching live data)
PROCLAIM_API_URL=https://proclaim-api-prod.mediassist.in/FetchVikingsRuleEngineLogs/
PROCLAIM_ACCESS_TOKEN=your_proclaim_access_token_here
PROCLAIM_PAYER=MTAwNHxNQTEyMzQ1Ng==
```

### 3. Run the Application

```bash
python main.py
```

Or using uvicorn directly:

```bash
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

## API Endpoints

### Data Upload
- `POST /upload-claims` - Upload JSON file
- `POST /upload-claims-json` - Upload JSON payload
- `POST /fetch-and-process` - Fetch claims from Proclaim API and process

### Q&A
- `POST /ask` - Ask questions about claims data
- `GET /claim/{claim_no}/summary` - Get claim summary
- `GET /claim/{claim_no}/questions` - Get suggested questions

### Management
- `GET /stats` - Database statistics
- `GET /health` - Health check
- `GET /proclaim-status` - Check Proclaim API status
- `DELETE /claim/{claim_no}` - Delete claim data

## Usage Examples

### 1. Fetch Claims Data from API

```python
import requests

# Fetch claims directly from Proclaim API
response = requests.post("http://localhost:8001/fetch-and-process", json={
    "claim_numbers": [131420765, 131420766]
})
print(response.json())
```

### 2. Upload Claims Data (Alternative)

```python
import requests
import json

# Load your claims data
with open('response.json', 'r') as f:
    claims_data = json.load(f)

# Upload to API
response = requests.post(
    "http://localhost:8001/upload-claims-json",
    json=claims_data
)
print(response.json())
```

### 3. Ask Questions

```python
# Ask about financial impact
response = requests.post("http://localhost:8001/ask", json={
    "question": "What was the total approved amount for this claim?",
    "claim_no": 131420765,  # Optional: filter by claim
    "max_results": 5
})

result = response.json()
print(f"Answer: {result['answer']}")
print(f"Confidence: {result['confidence_score']}")
```

### 4. Get Claim Summary

```python
response = requests.get("http://localhost:8001/claim/131420765/summary")
summary = response.json()
print(summary['summary'])
```

## Data Processing

The system processes complex claims data into structured chunks:

### Input Structure
```json
{
  "data": [
    {
      "claimNo": 131420765,
      "logs": [
        {
          "typeOfLog": "Claim",
          "ruleEngineLogs": [
            {
              "category": "NonMedicalExpense",
              "subCategory": "Standard",
              "preExecutionApprovedAmount": 0.0,
              "postExecutionApprovedAmount": 110163.96,
              // ... complex nested data
            }
          ]
        }
      ]
    }
  ]
}
```

### Processed Output
```
CLAIM PROCESSING RULE: Standard (IRDAI)
Category: NonMedicalExpense > Standard
Execution Time: 2025-05-15 10:30:45

FINANCIAL IMPACT:
Post-execution Bill: ₹119,565.90 | Total Deductions: ₹9,402.04

CONFIGURATION RULES:
PayableType: Bill For | IsPayable: Applicable | PayableLimit: 100000
```

## Question Examples

- "What was the total approved amount for claim 131420765?"
- "Which deductions were applied and why?"
- "What rules were executed during claim processing?"
- "What was the financial impact of each rule category?"
- "Were there any repudiation rules applied?"
- "What non-medical expenses were processed?"

## Testing

Run the test script to verify functionality:

```bash
python test_api.py
```

This will:
1. Test health endpoint
2. Upload sample claims data
3. Test Q&A functionality
4. Generate claim summaries
5. Get suggested questions

## API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8001/docs
- **ReDoc**: http://localhost:8001/redoc

## Configuration

### Vector Database
- **ChromaDB**: Persistent storage for embeddings
- **Embedding Model**: `all-MiniLM-L6-v2` (lightweight, fast)
- **Similarity Search**: Cosine similarity for context retrieval

### LLM Settings
- **Model**: GPT-3.5-turbo (configurable)
- **Max Tokens**: 4000 (adjustable)
- **Temperature**: 0.1 (factual responses)

### Performance
- **Chunk Size**: 1000 characters
- **Context Retrieval**: Top 5 relevant chunks
- **Token Optimization**: Only relevant context sent to LLM

## Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure valid API key in `.env`
2. **ChromaDB**: Check write permissions for `./chroma_db`
3. **Memory**: Large JSON files may require more RAM
4. **Dependencies**: Install all requirements with exact versions

### Logs
Check application logs for detailed error information.

## License

MIT License - see LICENSE file for details.
