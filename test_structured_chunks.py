#!/usr/bin/env python3
"""
Test script to verify that the data processor creates both granular and structured chunks
"""

import json
from data_processor import ClaimsDataProcessor
from models import ClaimsResponse, ClaimData, LogData, RuleEngineLog

def create_sample_claims_data():
    """Create sample claims data for testing"""
    
    # Sample rule engine log
    rule_log = RuleEngineLog(
        category="DEDUCTION",
        subCategory="COPAY",
        subCategoryDisplayName="Patient Copay",
        logMessages=[
            {"message": "Copay of 12.5% applied", "loggerType": "1"}
        ],
        matchingConfig=["rule1"],  # Non-empty to pass filtering
        postExecutionTotalBillAmount=10000  # Non-zero to pass filtering
    )
    
    # Sample log data
    log_data = LogData(
        typeOfLog="Claim",
        logCreatedDate="/Date(1640995200000)/",
        data=[
            {"message": "Claim processed", "timeOfOccurrence": "/Date(1640995200000)/"}
        ],
        ruleEngineLogs=[rule_log]
    )
    
    # Sample claim data
    claim_data = ClaimData(
        claimNo=131420765,
        logs=[log_data]
    )
    
    # Sample claims response
    claims_response = ClaimsResponse(data=[claim_data])
    
    return claims_response

def create_sample_claim_details():
    """Create sample claim details for testing"""
    return {
        "claimDetails": [{
            "id": 131420765,
            "mainNumber": 131420765,
            "claimInfo": {
                "claimNo": 131420765,
                "approvedAmt": 50000,
                "netPayAmount": 45000
            },
            "ailmentSmry": {
                "ailmentDesc": "Test ailment",
                "treatmentType": "Inpatient"
            },
            "hospHeader": {
                "hospName": "Test Hospital",
                "hospCity": "Test City"
            },
            "benefDtls": {
                "memberName": "Test Patient",
                "policyNo": "TEST123"
            },
            "missingDocuments": [],
            "deductionSummary": [
                {
                    "deductionDesc": "Copay",
                    "amount": 5000
                }
            ],
            "billSummary": [
                {
                    "billDesc": "Room charges",
                    "nonPayableAmt": 2000
                }
            ],
            # Fields that should be removed
            "canMoveToLC": True,
            "accountInfo": {},
            "paymentDtls": {},
            "allPaymentDtls": [],
            "claimIntimationDetails": {},
            "relatedPreAuths": [],
            "paDeductionSummary": []
        }]
    }

def test_structured_chunks_creation():
    """Test that structured chunks are created from mock debug data"""
    print("Testing structured chunks creation from mock debug data...")

    # Create mock debug data (same structure as the real debug file)
    debug_data = {
        "DeductionsInClaim": [
            {
                "deductionDesc": "Copay",
                "amount": 5000
            }
        ],
        "BasicClaimInformation": {
            "id": *********,
            "mainNumber": *********,
            "approvedAmt": 50000,
            "netPayAmount": 45000
        },
        "AilmentSummary": {
            "ailmentDesc": "Test ailment",
            "treatmentType": "Inpatient"
        },
        "HospitalDetails": {
            "hospName": "Test Hospital",
            "hospCity": "Test City"
        },
        "PatientAndPolicyDetails": {
            "memberName": "Test Patient",
            "policyNo": "TEST123"
        },
        "MissingDocuments": [],
        "Deduction reasons": [
            {
                "claimNo": *********,
                "logs": [
                    {
                        "ruleEngineLogs": {
                            "DEDUCTION": {
                                "rules": [
                                    {
                                        "subCategoryDisplayName": "Copay",
                                        "reasons": [
                                            {"message": "12.5% copay applied"}
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        ]
    }
    print("✅ Created mock debug data")

    # Initialize processor
    processor = ClaimsDataProcessor()

    # Test the _create_chunks_from_structured_output method directly
    chunks = processor._create_chunks_from_structured_output(debug_data, None)
    
    print(f"Total structured chunks created: {len(chunks)}")

    # Print chunk types
    print("\nStructured chunk types:")
    for chunk in chunks:
        print(f"  - {chunk.chunk_type} (claim: {chunk.claim_no})")
        print(f"    Section: {chunk.metadata.get('section_name', 'Unknown')}")
        print(f"    Content preview: {chunk.content[:100]}...")

    # Check if we have the expected structured sections
    expected_sections = ['DeductionsInClaim', 'BasicClaimInformation', 'AilmentSummary',
                       'HospitalDetails', 'PatientAndPolicyDetails', 'MissingDocuments']

    found_sections = [chunk.metadata.get('section_name') for chunk in chunks]

    print(f"\nExpected sections: {expected_sections}")
    print(f"Found sections: {found_sections}")

    missing_sections = [section for section in expected_sections if section not in found_sections]
    if missing_sections:
        print(f"⚠️  Missing sections: {missing_sections}")
    else:
        print("✅ All expected sections found!")

    if len(chunks) > 0:
        print("\n✅ SUCCESS: Structured chunks were created from debug data!")
    else:
        print("\n❌ FAILURE: No structured chunks created")
    
    return chunks

def test_debug_file_creation():
    """Test that debug file is created with the same structure as structured chunks"""
    print("\n" + "="*50)
    print("Testing debug file creation...")
    
    # Check if a debug file was created
    import os
    import glob
    
    debug_files = glob.glob("debug_claims_response_*.json")
    if debug_files:
        latest_debug_file = max(debug_files, key=os.path.getctime)
        print(f"✅ Debug file created: {latest_debug_file}")
        
        # Load and examine the debug file
        with open(latest_debug_file, 'r') as f:
            debug_data = json.load(f)
        
        print("Debug file structure:")
        for key in debug_data.keys():
            if not key.startswith('_'):
                print(f"  - {key}")
        
        return debug_data
    else:
        print("❌ No debug file found")
        return None

def main():
    """Main test function"""
    print("Testing Structured Chunks Creation in /fetch-and-process")
    print("=" * 60)
    
    # Test chunk creation
    chunks = test_structured_chunks_creation()
    
    # Test debug file creation
    debug_data = test_debug_file_creation()
    
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"- Total chunks created: {len(chunks)}")
    print(f"- Debug file created: {'Yes' if debug_data else 'No'}")
    
    if debug_data and len(chunks) > 0:
        print("✅ SUCCESS: Both structured chunks and debug file are working correctly!")
        print("\nThe /fetch-and-process endpoint now stores:")
        print("1. Granular chunks (individual rule logs, claim summaries)")
        print("2. Structured chunks (same sections as debug JSON)")
        print("3. Debug JSON file (for manual inspection)")
    else:
        print("❌ Some issues found. Check the output above.")

if __name__ == "__main__":
    main()
