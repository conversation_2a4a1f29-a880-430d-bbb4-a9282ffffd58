from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import json
import logging
import os
from typing import Dict, Any, List, Union
import uvicorn

from models import QuestionRequest, QuestionResponse, ClaimsResponse, FetchSingleClaimRequest, ProcessedClaimChunk
from data_processor import ClaimsDataProcessor
from vector_store import VectorStore
from qa_engine import QAEngine
from proclaim_client import ProclaimAPIClient
from config import settings
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Claims Data Q&A API",
    description="API for processing insurance claims data and answering questions using LLM",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize components
data_processor = ClaimsDataProcessor()
vector_store = VectorStore()
qa_engine = QAEngine()
proclaim_client = ProclaimAPIClient()

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Claims Data Q&A API",
        "version": "1.0.0",
        "endpoints": {
            "POST /upload-claims": "Upload and process claims data",
            "POST /fetch-and-process": "Fetch claims from API and process",
            "POST /ask": "Ask questions about claims data",
            "GET /claim/{claim_no}/summary": "Get claim summary",
            "GET /claim/{claim_no}/questions": "Get suggested questions for a claim",
            "GET /stats": "Get database statistics",
            "GET /proclaim-status": "Check Proclaim API status",
            "DELETE /claim/{claim_no}": "Delete claim data"
        }
    }

@app.post("/upload-claims")
async def upload_claims(file: UploadFile = File(...)):
    """Upload and process claims data from JSON file"""
    try:
        # Validate file type
        if not file.filename.endswith('.json'):
            raise HTTPException(status_code=400, detail="Only JSON files are supported")
        
        # Read and parse JSON
        content = await file.read()
        try:
            claims_data = json.loads(content)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON format: {str(e)}")
        
        # Process the claims data
        logger.info("Processing claims data...")
        processed_chunks = data_processor.process_claims_data(claims_data)
        
        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the uploaded file")
        
        # Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")
        
        # Analyze processed data
        claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        chunk_types = {}
        for chunk in processed_chunks:
            chunk_types[chunk.chunk_type] = chunk_types.get(chunk.chunk_type, 0) + 1
        
        return {
            "message": "Claims data processed and stored successfully",
            "processed_chunks": len(processed_chunks),
            "claims_processed": claim_numbers,
            "chunk_types": chunk_types
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/upload-claims-json")
async def upload_claims_json(claims_data: Union[Dict[str, Any], List[Dict[str, Any]]]):
    """Upload and process claims data from JSON payload (supports both old and new formats)"""
    try:
        # Process the claims data
        logger.info("Processing claims data from JSON payload...")
        processed_chunks = data_processor.process_claims_data(claims_data)
        
        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the payload")
        
        # Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")
        
        # Analyze processed data
        claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        chunk_types = {}
        for chunk in processed_chunks:
            chunk_types[chunk.chunk_type] = chunk_types.get(chunk.chunk_type, 0) + 1
        
        return {
            "message": "Claims data processed and stored successfully",
            "processed_chunks": len(processed_chunks),
            "claims_processed": claim_numbers,
            "chunk_types": chunk_types
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/fetch-and-process")
async def fetch_and_process_claims(request: FetchSingleClaimRequest):
    """Enhanced fetch-and-process: Takes one claim number, extracts related pre-auth IDs, fetches rule logs for all"""
    try:
        claim_number = request.claim_number
        logger.info(f"Processing single claim with enhanced workflow: {claim_number}")

        # Validate Proclaim API configuration
        if not proclaim_client.validate_configuration():
            raise HTTPException(status_code=500, detail="Proclaim API is not properly configured")

        # Step 1: Fetch claim details to get related pre-auth IDs
        claim_details = proclaim_client.fetch_claim_details(
            claim_number=claim_number,
            login_name=request.login_name,
            login_email=request.login_email,
            login_user_id=request.login_user_id
        )

        if not claim_details:
            raise HTTPException(status_code=500, detail=f"Failed to fetch claim details for claim {claim_number}")

        # Step 2: Extract related pre-auth IDs
        related_preauth_ids = proclaim_client.extract_related_preauth_ids(claim_details)
        logger.info(f"Extracted related pre-auth IDs: {related_preauth_ids}")

        # Step 3: Combine original claim number with related pre-auth IDs for rule engine logs
        all_claim_ids = [claim_number] + related_preauth_ids
        unique_claim_ids = list(set(all_claim_ids))  # Remove duplicates
        logger.info(f"Fetching rule engine logs for all claim IDs: {unique_claim_ids}")

        # Step 4: Fetch rule engine logs for all claim IDs
        claims_data = proclaim_client.fetch_claims_data(
            unique_claim_ids,
            login_name=request.login_name,
            login_email=request.login_email,
            login_user_id=request.login_user_id
        )

        if not claims_data:
            raise HTTPException(status_code=500, detail="Failed to fetch rule engine logs from Proclaim API")

        # Step 5: Process the claims data with parent claim number
        logger.info("Processing fetched claims data...")
        processed_chunks = data_processor.process_claims_data(claims_data, parent_claim_no=claim_number, claim_details=claim_details)

        if not processed_chunks:
            raise HTTPException(status_code=400, detail="No valid data found in the fetched response")

        # Step 6: Store in vector database
        logger.info(f"Storing {len(processed_chunks)} chunks in vector database...")
        success = vector_store.add_chunks(processed_chunks)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to store data in vector database")

        # Analyze processed data
        processed_claim_numbers = list(set(chunk.claim_no for chunk in processed_chunks))
        

        return {
            "message": "Claims data fetched and processed successfully with enhanced workflow",
            "main_claim_number": claim_number,
            "related_preauth_ids": related_preauth_ids,
            "all_processed_claim_ids": unique_claim_ids,
            "processed_claims": processed_claim_numbers,
            "processed_chunks": len(processed_chunks),
            "workflow": "enhanced"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching and processing claims data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/proclaim-status")
async def get_proclaim_status():
    """Get Proclaim API status and configuration"""
    try:
        api_info = proclaim_client.get_api_info()

        # Test connection if configuration is valid
        connection_test = False
        if api_info["configuration_valid"]:
            connection_test = proclaim_client.test_connection()

        return {
            "api_info": api_info,
            "connection_test_passed": connection_test,
            "status": "ready" if connection_test else "not_ready"
        }

    except Exception as e:
        logger.error(f"Error checking Proclaim status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/ask", response_model=QuestionResponse)
async def ask_question(request: QuestionRequest):
    """Ask a question about the claims data"""
    try:
        # Question is mandatory
        if not request.question or not request.question.strip():
            raise HTTPException(status_code=400, detail="Question cannot be empty")

        logger.info(f"Processing question: {request.question}")
        if request.claim_no:
            logger.info(f"Using full context for claim: {request.claim_no}")

        # Get answer from QA engine
        # If claim_no is provided, it will use full context instead of vector search
        result = qa_engine.answer_question(
            question=request.question,
            claim_no=request.claim_no,
            max_results=request.max_results
        )

        return QuestionResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing question: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/claim/{claim_no}/summary")
async def get_claim_summary(claim_no: int):
    """Get a comprehensive summary of a specific claim"""
    try:
        logger.info(f"Generating summary for claim {claim_no}")
        
        summary = qa_engine.get_claim_summary(claim_no)
        return summary
        
    except Exception as e:
        logger.error(f"Error generating claim summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/claim/{claim_no}/questions")
async def get_suggested_questions(claim_no: int):
    """Get suggested questions for a specific claim"""
    try:
        logger.info(f"Getting suggested questions for claim {claim_no}")
        
        questions = qa_engine.suggest_questions(claim_no)
        return {
            "claim_no": claim_no,
            "suggested_questions": questions
        }
        
    except Exception as e:
        logger.error(f"Error getting suggested questions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/questions")
async def get_general_questions():
    """Get general suggested questions"""
    try:
        questions = qa_engine.suggest_questions()
        return {
            "suggested_questions": questions
        }
        
    except Exception as e:
        logger.error(f"Error getting general questions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get database statistics"""
    try:
        stats = vector_store.get_collection_stats()
        return {
            "database_stats": stats,
            "api_info": {
                "embedding_model": settings.EMBEDDING_MODEL,
                "llm_model": settings.GEMINI_MODEL,
                "max_tokens": settings.MAX_TOKENS
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.delete("/claim/{claim_no}")
async def delete_claim_data(claim_no: int):
    """Delete all data for a specific claim"""
    try:
        logger.info(f"Deleting data for claim {claim_no}")
        
        success = vector_store.delete_claim_data(claim_no)
        
        if success:
            return {"message": f"Successfully deleted data for claim {claim_no}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete claim data")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting claim data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/process-debug-data")
async def process_debug_data():
    """Process debug_claims_response_20250614_001219.json and store in ChromaDB"""
    try:
        debug_file_path = "debug_claims_response_20250614_001219.json"

        # Check if file exists
        if not os.path.exists(debug_file_path):
            raise HTTPException(status_code=404, detail=f"Debug file not found: {debug_file_path}")

        # Load debug data
        with open(debug_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract main claim number
        basic_info = data.get('BasicClaimInformation', {})
        main_claim_no = basic_info.get('mainNumber') or basic_info.get('id')

        if not main_claim_no:
            raise HTTPException(status_code=400, detail="Could not extract main claim number from debug data")

        main_claim_no = int(main_claim_no)
        logger.info(f"Processing debug data for main claim: {main_claim_no}")

        # Delete existing data for this claim
        vector_store.delete_claim_data(main_claim_no)

        # Create chunks from debug data
        chunks = []
        timestamp = datetime.now().isoformat()

        # Process main sections
        sections = {
            'DeductionsInClaim': 'deduction_summary',
            'Deduction reasons': 'rule_engine_log',
            'BasicClaimInformation': 'claim_summary',
            'AilmentSummary': 'ailment_summary',
            'HospitalDetails': 'hospital_details',
            'PatientAndPolicyDetails': 'patient_policy_details',
            'MissingDocuments': 'missing_documents'
        }

        for section_name, chunk_type in sections.items():
            section_data = data.get(section_name)
            if section_data is not None:
                # Create content string
                content = f"=== {section_name} ===\n"
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        if not key.startswith('_'):  # Skip metadata
                            if isinstance(value, (dict, list)):
                                content += f"{key}: {json.dumps(value, indent=2)[:1000]}...\n" if len(str(value)) > 1000 else f"{key}: {json.dumps(value, indent=2)}\n"
                            else:
                                content += f"{key}: {str(value)}\n"
                elif isinstance(section_data, list):
                    for i, item in enumerate(section_data):
                        if isinstance(item, (dict, list)):
                            content += f"Item {i+1}: {json.dumps(item, indent=2)[:1000]}...\n" if len(str(item)) > 1000 else f"Item {i+1}: {json.dumps(item, indent=2)}\n"
                        else:
                            content += f"Item {i+1}: {str(item)}\n"
                else:
                    content += str(section_data)

                # Create chunk
                chunk_id = f"{main_claim_no}_{chunk_type}_{timestamp.replace(':', '_').replace('.', '_')}"
                chunk = ProcessedClaimChunk(
                    claim_no=main_claim_no,
                    chunk_id=chunk_id,
                    chunk_type=chunk_type,
                    content=content,
                    metadata={
                        'section_name': section_name,
                        'source': 'debug_file',
                        'category': chunk_type
                    },
                    timestamp=timestamp
                )
                chunks.append(chunk)

        # Store in vector database
        success = vector_store.add_chunks(chunks)

        if success:
            return {
                "success": True,
                "main_claim_no": main_claim_no,
                "chunks_created": len(chunks),
                "message": f"Debug data processed and stored for claim {main_claim_no}"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to store chunks in vector database")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing debug data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test vector store connection
        stats = vector_store.get_collection_stats()
        
        return {
            "status": "healthy",
            "vector_store": "connected",
            "total_chunks": stats.get("total_chunks", 0)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=True
    )
