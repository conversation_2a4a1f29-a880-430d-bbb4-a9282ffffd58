<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claims Data Q&A</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Claims Data Q&A System</h1>
        
        <!-- Status Section -->
        <div class="section">
            <h2>System Status</h2>
            <button onclick="checkHealth()">Check Health</button>
            <button onclick="getStats()">Get Statistics</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <!-- Upload Section -->
        <div class="section">
            <h2>Upload Claims Data</h2>
            <div class="form-group">
                <label for="fileInput">Select JSON File:</label>
                <input type="file" id="fileInput" accept=".json">
            </div>
            <button onclick="uploadFile()">Upload Claims Data</button>
            <div id="uploadResult" class="result" style="display: none;"></div>
        </div>

        <!-- Fetch from API Section -->
        <div class="section">
            <h2>Fetch Claims from Proclaim API</h2>
            <div class="form-group">
                <label for="claimNumbersInput">Claim Numbers (comma-separated):</label>
                <input type="text" id="claimNumbersInput" placeholder="e.g., 131420765, 131420766">
            </div>
            <button onclick="checkProclaimStatus()">Check API Status</button>
            <button onclick="fetchAndProcessClaims()">Fetch & Process Claims</button>
            <div id="fetchResult" class="result" style="display: none;"></div>
        </div>

        <!-- Q&A Section -->
        <div class="section">
            <h2>Ask Questions</h2>
            <div class="form-group">
                <label for="questionInput">Your Question:</label>
                <textarea id="questionInput" rows="3" placeholder="e.g., What was the total approved amount for this claim?"></textarea>
            </div>
            <div class="form-group">
                <label for="claimNoInput">Claim Number (optional):</label>
                <input type="number" id="claimNoInput" placeholder="e.g., 131420765">
            </div>
            <div class="form-group">
                <label for="maxResultsInput">Max Results:</label>
                <select id="maxResultsInput">
                    <option value="3">3</option>
                    <option value="5" selected>5</option>
                    <option value="10">10</option>
                </select>
            </div>
            <button onclick="askQuestion()">Ask Question</button>
            <button onclick="getSuggestedQuestions()">Get Suggested Questions</button>
            <div class="loading" id="qaLoading">
                <div class="spinner"></div>
                <p>Processing your question...</p>
            </div>
            <div id="qaResult" class="result" style="display: none;"></div>
        </div>

        <!-- Claim Summary Section -->
        <div class="section">
            <h2>Claim Summary</h2>
            <div class="form-group">
                <label for="summaryClaimNo">Claim Number:</label>
                <input type="number" id="summaryClaimNo" placeholder="e.g., 131420765">
            </div>
            <button onclick="getClaimSummary()">Get Summary</button>
            <div id="summaryResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';

        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('statusResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('statusResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                let statsHtml = '<div class="stats">';
                if (data.database_stats) {
                    const stats = data.database_stats;
                    statsHtml += `
                        <div class="stat-card">
                            <div class="stat-number">${stats.total_chunks || 0}</div>
                            <div class="stat-label">Total Chunks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.unique_claims || 0}</div>
                            <div class="stat-label">Unique Claims</div>
                        </div>
                    `;
                }
                statsHtml += '</div>';
                statsHtml += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                showResult('statusResult', statsHtml, 'info');
            } catch (error) {
                showResult('statusResult', `Error: ${error.message}`, 'error');
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                showResult('uploadResult', 'Please select a file', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${API_BASE}/upload-claims`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('uploadResult', JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('uploadResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('uploadResult', `Error: ${error.message}`, 'error');
            }
        }

        async function checkProclaimStatus() {
            try {
                const response = await fetch(`${API_BASE}/proclaim-status`);
                const data = await response.json();

                if (response.ok) {
                    let statusHtml = `<strong>Proclaim API Status:</strong> ${data.status}\n\n`;
                    statusHtml += `<strong>Configuration:</strong>\n`;
                    statusHtml += `- API URL: ${data.api_info.api_url}\n`;
                    statusHtml += `- Has Access Token: ${data.api_info.has_access_token}\n`;
                    statusHtml += `- Payer: ${data.api_info.payer}\n`;
                    statusHtml += `- Configuration Valid: ${data.api_info.configuration_valid}\n`;
                    statusHtml += `- Connection Test: ${data.connection_test_passed ? 'PASSED' : 'FAILED'}\n`;

                    const resultType = data.connection_test_passed ? 'success' : 'error';
                    showResult('fetchResult', statusHtml, resultType);
                } else {
                    showResult('fetchResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('fetchResult', `Error: ${error.message}`, 'error');
            }
        }

        async function fetchAndProcessClaims() {
            const claimNumbersInput = document.getElementById('claimNumbersInput').value.trim();

            if (!claimNumbersInput) {
                showResult('fetchResult', 'Please enter claim numbers', 'error');
                return;
            }

            try {
                // Parse claim numbers
                const claimNumbers = claimNumbersInput.split(',').map(num => parseInt(num.trim())).filter(num => !isNaN(num));

                if (claimNumbers.length === 0) {
                    showResult('fetchResult', 'Please enter valid claim numbers', 'error');
                    return;
                }

                const requestBody = {
                    claim_number: claimNumbers[0]
                };

                const response = await fetch(`${API_BASE}/fetch-and-process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (response.ok) {
                    let resultHtml = `<strong>Fetch and Process Results:</strong>\n\n`;
                    resultHtml += `Message: ${data.message}\n`;
                    resultHtml += `Requested Claims: ${data.requested_claims.join(', ')}\n`;
                    resultHtml += `Processed Claims: ${data.processed_claims.join(', ')}\n`;
                    resultHtml += `Total Chunks: ${data.processed_chunks}\n\n`;
                    resultHtml += `<strong>Chunk Types:</strong>\n`;
                    for (const [type, count] of Object.entries(data.chunk_types)) {
                        resultHtml += `- ${type}: ${count}\n`;
                    }

                    showResult('fetchResult', resultHtml, 'success');
                } else {
                    showResult('fetchResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('fetchResult', `Error: ${error.message}`, 'error');
            }
        }

        async function askQuestion() {
            const question = document.getElementById('questionInput').value.trim();
            const claimNo = document.getElementById('claimNoInput').value;
            const maxResults = document.getElementById('maxResultsInput').value;

            if (!question) {
                showResult('qaResult', 'Please enter a question', 'error');
                return;
            }

            const loading = document.getElementById('qaLoading');
            loading.style.display = 'block';
            document.getElementById('qaResult').style.display = 'none';

            try {
                const requestBody = {
                    question: question,
                    max_results: parseInt(maxResults)
                };

                if (claimNo) {
                    requestBody.claim_no = parseInt(claimNo);
                }

                const response = await fetch(`${API_BASE}/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                loading.style.display = 'none';

                if (response.ok) {
                    let resultHtml = `<strong>Question:</strong> ${data.question}\n\n`;
                    resultHtml += `<strong>Answer:</strong>\n${data.answer}\n\n`;
                    resultHtml += `<strong>Confidence Score:</strong> ${(data.confidence_score * 100).toFixed(1)}%\n\n`;
                    resultHtml += `<strong>Relevant Chunks:</strong> ${data.relevant_chunks.length}\n`;
                    
                    if (data.relevant_chunks.length > 0) {
                        resultHtml += '\n<strong>Sources:</strong>\n';
                        data.relevant_chunks.forEach((chunk, index) => {
                            resultHtml += `${index + 1}. Claim ${chunk.claim_no} (${chunk.chunk_type}) - Similarity: ${(chunk.similarity_score * 100).toFixed(1)}%\n`;
                        });
                    }

                    showResult('qaResult', resultHtml, 'success');
                } else {
                    showResult('qaResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                loading.style.display = 'none';
                showResult('qaResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getSuggestedQuestions() {
            const claimNo = document.getElementById('claimNoInput').value;
            
            try {
                let url = `${API_BASE}/questions`;
                if (claimNo) {
                    url = `${API_BASE}/claim/${claimNo}/questions`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    let resultHtml = '<strong>Suggested Questions:</strong>\n\n';
                    data.suggested_questions.forEach((question, index) => {
                        resultHtml += `${index + 1}. ${question}\n`;
                    });

                    showResult('qaResult', resultHtml, 'info');
                } else {
                    showResult('qaResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('qaResult', `Error: ${error.message}`, 'error');
            }
        }

        async function getClaimSummary() {
            const claimNo = document.getElementById('summaryClaimNo').value;

            if (!claimNo) {
                showResult('summaryResult', 'Please enter a claim number', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/claim/${claimNo}/summary`);
                const data = await response.json();

                if (response.ok) {
                    let resultHtml = `<strong>Claim ${data.claim_no} Summary:</strong>\n\n`;
                    resultHtml += data.summary;

                    if (data.details) {
                        resultHtml += '\n\n<strong>Details:</strong>\n';
                        resultHtml += JSON.stringify(data.details, null, 2);
                    }

                    showResult('summaryResult', resultHtml, 'success');
                } else {
                    showResult('summaryResult', `Error: ${data.detail}`, 'error');
                }
            } catch (error) {
                showResult('summaryResult', `Error: ${error.message}`, 'error');
            }
        }

        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Auto-fill claim number from sample data
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('claimNoInput').value = '131420765';
            document.getElementById('summaryClaimNo').value = '131420765';
        });
    </script>
</body>
</html>
